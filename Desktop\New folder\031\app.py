from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_file, abort
from flask_login import Lo<PERSON><PERSON><PERSON>ger, login_user, logout_user, login_required, current_user
from werkzeug.utils import secure_filename
import os
from datetime import datetime, date
from sqlalchemy import or_, and_

# Import our modules
from config import Config
from models import db, User, Document, Category, DocumentType, DocumentComment, ActivityLog, SystemSettings
from forms import (LoginForm, DocumentUploadForm, DocumentEditForm, SearchForm, 
                  CommentForm, UserForm, CategoryForm, DocumentTypeForm, ChangePasswordForm)
from utils import (allowed_file, generate_unique_filename, get_file_info, organize_file_path,
                  generate_registration_number, log_activity, format_file_size, get_file_icon_class)

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # Initialize extensions
    db.init_app(app)
    
    # Initialize Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # Initialize configuration
    Config.init_app(app)
    
    return app

app = create_app()

# Routes
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        
        if user and user.check_password(form.password.data) and user.is_active:
            login_user(user, remember=form.remember_me.data)
            user.last_login = datetime.utcnow()
            db.session.commit()
            
            # Log login activity
            log_activity(
                user_id=user.id,
                action='login',
                description='تسجيل دخول المستخدم',
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent')
            )
            
            next_page = request.args.get('next')
            if not next_page or not next_page.startswith('/'):
                next_page = url_for('dashboard')
            return redirect(next_page)
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('login.html', form=form)

@app.route('/logout')
@login_required
def logout():
    log_activity(
        user_id=current_user.id,
        action='logout',
        description='تسجيل خروج المستخدم',
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent')
    )
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    # Get statistics
    total_documents = Document.query.filter_by(status='active').count()
    my_documents = Document.query.filter_by(uploaded_by=current_user.id, status='active').count()
    archived_documents = Document.query.filter_by(is_archived=True, status='active').count()
    confidential_documents = Document.query.filter_by(is_confidential=True, status='active').count()
    
    # Get recent documents
    recent_documents = Document.query.filter_by(status='active').order_by(Document.created_at.desc()).limit(10).all()
    
    # Get recent activities
    recent_activities = ActivityLog.query.order_by(ActivityLog.created_at.desc()).limit(10).all()
    
    stats = {
        'total_documents': total_documents,
        'my_documents': my_documents,
        'archived_documents': archived_documents,
        'confidential_documents': confidential_documents
    }
    
    return render_template('dashboard.html', 
                         stats=stats, 
                         recent_documents=recent_documents,
                         recent_activities=recent_activities)

@app.route('/upload', methods=['GET', 'POST'])
@login_required
def upload_document():
    if not current_user.has_permission('upload'):
        flash('ليس لديك صلاحية لرفع الوثائق', 'error')
        return redirect(url_for('dashboard'))
    
    form = DocumentUploadForm()
    
    # Populate choices for select fields
    form.category_id.choices = [(c.id, c.name_ar) for c in Category.query.filter_by(is_active=True).all()]
    form.document_type_id.choices = [(dt.id, dt.name_ar) for dt in DocumentType.query.all()]
    
    if form.validate_on_submit():
        file = form.file.data
        
        if file and allowed_file(file.filename):
            try:
                # Generate unique filename
                filename = generate_unique_filename(file.filename)
                
                # Get category for file organization
                category = Category.query.get(form.category_id.data)
                file_path = organize_file_path(filename, category.name_en)
                full_path = os.path.join(app.config['UPLOAD_FOLDER'], 'documents', file_path)
                
                # Save file
                file.save(full_path)
                
                # Get file information
                file_info = get_file_info(full_path)
                
                # Generate registration number
                reg_number = generate_registration_number(category.name_en[:3].upper())
                
                # Create document record
                document = Document(
                    registration_number=reg_number,
                    title=form.title.data,
                    description=form.description.data,
                    sender=form.sender.data,
                    receiver=form.receiver.data,
                    subject=form.subject.data,
                    filename=filename,
                    original_filename=file.filename,
                    file_path=file_path,
                    file_size=file_info['size'],
                    file_type=file.filename.split('.')[-1].lower(),
                    mime_type=file_info['mime_type'],
                    document_date=form.document_date.data,
                    received_date=form.received_date.data,
                    category_id=form.category_id.data,
                    document_type_id=form.document_type_id.data,
                    uploaded_by=current_user.id,
                    is_confidential=form.is_confidential.data
                )
                
                db.session.add(document)
                db.session.commit()
                
                # Add tags if provided
                if form.tags.data:
                    from models import DocumentTag
                    tags = [tag.strip() for tag in form.tags.data.split(',') if tag.strip()]
                    for tag_name in tags:
                        tag = DocumentTag(document_id=document.id, tag_name=tag_name)
                        db.session.add(tag)
                
                db.session.commit()
                
                # Log activity
                log_activity(
                    user_id=current_user.id,
                    action='upload',
                    document_id=document.id,
                    description=f'رفع وثيقة جديدة: {document.title}',
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent')
                )
                
                flash(f'تم رفع الوثيقة بنجاح. رقم التسجيل: {reg_number}', 'success')
                return redirect(url_for('view_document', id=document.id))
                
            except Exception as e:
                db.session.rollback()
                flash(f'حدث خطأ أثناء رفع الوثيقة: {str(e)}', 'error')
        else:
            flash('نوع الملف غير مدعوم', 'error')
    
    return render_template('upload.html', form=form)

@app.route('/search')
@login_required
def search_documents():
    form = SearchForm()
    
    # Populate choices
    form.category_id.choices = [('', 'جميع التصنيفات')] + [(c.id, c.name_ar) for c in Category.query.filter_by(is_active=True).all()]
    form.document_type_id.choices = [('', 'جميع الأنواع')] + [(dt.id, dt.name_ar) for dt in DocumentType.query.all()]
    
    documents = []
    page = request.args.get('page', 1, type=int)
    
    if request.args.get('query') or any(request.args.get(field) for field in ['category_id', 'document_type_id', 'sender', 'receiver', 'date_from', 'date_to']):
        # Build query
        query = Document.query.filter_by(status='active')
        
        # Text search
        if request.args.get('query'):
            search_term = f"%{request.args.get('query')}%"
            query = query.filter(
                or_(
                    Document.title.like(search_term),
                    Document.description.like(search_term),
                    Document.subject.like(search_term),
                    Document.sender.like(search_term),
                    Document.receiver.like(search_term),
                    Document.registration_number.like(search_term)
                )
            )
        
        # Category filter
        if request.args.get('category_id'):
            query = query.filter_by(category_id=request.args.get('category_id'))
        
        # Document type filter
        if request.args.get('document_type_id'):
            query = query.filter_by(document_type_id=request.args.get('document_type_id'))
        
        # Sender filter
        if request.args.get('sender'):
            query = query.filter(Document.sender.like(f"%{request.args.get('sender')}%"))
        
        # Receiver filter
        if request.args.get('receiver'):
            query = query.filter(Document.receiver.like(f"%{request.args.get('receiver')}%"))
        
        # Date filters
        if request.args.get('date_from'):
            date_from = datetime.strptime(request.args.get('date_from'), '%Y-%m-%d').date()
            query = query.filter(Document.document_date >= date_from)
        
        if request.args.get('date_to'):
            date_to = datetime.strptime(request.args.get('date_to'), '%Y-%m-%d').date()
            query = query.filter(Document.document_date <= date_to)
        
        # Confidential filter
        if request.args.get('is_confidential'):
            is_conf = request.args.get('is_confidential') == '1'
            query = query.filter_by(is_confidential=is_conf)
        
        # Archived filter
        if request.args.get('is_archived'):
            is_arch = request.args.get('is_archived') == '1'
            query = query.filter_by(is_archived=is_arch)
        
        # Check permissions for confidential documents
        if current_user.role == 'viewer':
            query = query.filter_by(is_confidential=False)
        
        # Paginate results
        documents = query.order_by(Document.created_at.desc()).paginate(
            page=page, per_page=app.config['DOCUMENTS_PER_PAGE'], error_out=False
        )
    
    return render_template('search.html', form=form, documents=documents)
