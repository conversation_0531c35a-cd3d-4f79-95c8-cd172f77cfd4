{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام الأرشيف الإلكتروني{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>
            لوحة التحكم
        </h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_documents }}</h4>
                        <p class="card-text">إجمالي الوثائق</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.my_documents }}</h4>
                        <p class="card-text">وثائقي</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-edit fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.archived_documents }}</h4>
                        <p class="card-text">الوثائق المؤرشفة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-archive fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.confidential_documents }}</h4>
                        <p class="card-text">الوثائق السرية</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-lock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if current_user.has_permission('upload') %}
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('upload_document') }}" class="btn btn-primary w-100">
                            <i class="fas fa-upload me-2"></i>
                            رفع وثيقة جديدة
                        </a>
                    </div>
                    {% endif %}

                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('search_documents') }}" class="btn btn-info w-100">
                            <i class="fas fa-search me-2"></i>
                            البحث في الوثائق
                        </a>
                    </div>

                    {% if current_user.role == 'admin' %}
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('admin_panel') }}" class="btn btn-warning w-100">
                            <i class="fas fa-cog me-2"></i>
                            لوحة الإدارة
                        </a>
                    </div>
                    {% endif %}

                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('profile') }}" class="btn btn-secondary w-100">
                            <i class="fas fa-user me-2"></i>
                            الملف الشخصي
                        </a>
                    </div>


                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Documents and Activities -->
<div class="row">
    <!-- Recent Documents -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>
                    الوثائق الحديثة
                </h5>
                <a href="{{ url_for('search_documents') }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% if recent_documents %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>العنوان</th>
                                <th>رقم التسجيل</th>
                                <th>التصنيف</th>
                                <th>تاريخ الرفع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for doc in recent_documents %}
                            {% if not (doc.is_confidential and current_user.role == 'viewer') %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="{{ get_file_icon_class(doc.filename) }} me-2"></i>
                                        {{ doc.title[:50] }}{% if doc.title|length > 50 %}...{% endif %}
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ doc.registration_number }}</span>
                                </td>
                                <td>{{ doc.category.name_ar }}</td>
                                <td>{{ doc.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <a href="{{ url_for('view_document', id=doc.id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endif %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p>لا توجد وثائق حديثة</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recent Activities -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    النشاطات الأخيرة
                </h5>
            </div>
            <div class="card-body">
                {% if recent_activities %}
                <div class="activity-feed">
                    {% for activity in recent_activities %}
                    <div class="activity-item mb-3 pb-3 border-bottom">
                        <div class="d-flex">
                            <div class="activity-icon me-3">
                                {% if activity.action == 'upload' %}
                                    <i class="fas fa-upload text-success"></i>
                                {% elif activity.action == 'view' %}
                                    <i class="fas fa-eye text-info"></i>
                                {% elif activity.action == 'edit' %}
                                    <i class="fas fa-edit text-warning"></i>
                                {% elif activity.action == 'delete' %}
                                    <i class="fas fa-trash text-danger"></i>
                                {% elif activity.action == 'download' %}
                                    <i class="fas fa-download text-primary"></i>
                                {% else %}
                                    <i class="fas fa-circle text-secondary"></i>
                                {% endif %}
                            </div>
                            <div class="activity-content">
                                <div class="activity-user fw-bold">{{ activity.user.full_name }}</div>
                                <div class="activity-description small text-muted">
                                    {{ activity.description }}
                                </div>
                                <div class="activity-time small text-muted">
                                    {{ activity.created_at.strftime('%Y-%m-%d %H:%M') }}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-history fa-3x mb-3"></i>
                    <p>لا توجد نشاطات حديثة</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        transition: box-shadow 0.15s ease-in-out;
    }

    .card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .activity-feed {
        max-height: 400px;
        overflow-y: auto;
    }

    .activity-item:last-child {
        border-bottom: none !important;
    }

    .activity-icon {
        width: 20px;
        text-align: center;
    }
</style>
{% endblock %}
