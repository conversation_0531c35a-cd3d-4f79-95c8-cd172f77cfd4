{% extends "base.html" %}

{% block title %}{{ document.title }} - نظام الأرشيف الإلكتروني{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('search_documents') }}">البحث</a></li>
                <li class="breadcrumb-item active">{{ document.title[:30] }}{% if document.title|length > 30 %}...{% endif %}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Document Details -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">
                    <i class="{{ get_file_icon_class(document.filename) }} me-2"></i>
                    {{ document.title }}
                </h4>
            </div>
            <div class="card-body">
                <!-- Registration Number -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>رقم التسجيل:</strong>
                        <span class="badge bg-primary ms-2">{{ document.registration_number }}</span>
                    </div>
                    <div class="col-md-6">
                        <strong>تاريخ الرفع:</strong>
                        <span class="text-muted">{{ document.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                    </div>
                </div>

                <!-- Description -->
                {% if document.description %}
                <div class="mb-3">
                    <strong>الوصف:</strong>
                    <p class="mt-2">{{ document.description }}</p>
                </div>
                {% endif %}

                <!-- Correspondence Info -->
                <div class="row mb-3">
                    {% if document.sender %}
                    <div class="col-md-6">
                        <strong>المرسل:</strong>
                        <span class="text-muted">{{ document.sender }}</span>
                    </div>
                    {% endif %}
                    {% if document.receiver %}
                    <div class="col-md-6">
                        <strong>المستقبل:</strong>
                        <span class="text-muted">{{ document.receiver }}</span>
                    </div>
                    {% endif %}
                </div>

                {% if document.subject %}
                <div class="mb-3">
                    <strong>الموضوع:</strong>
                    <span class="text-muted">{{ document.subject }}</span>
                </div>
                {% endif %}

                <!-- Dates -->
                <div class="row mb-3">
                    {% if document.document_date %}
                    <div class="col-md-6">
                        <strong>تاريخ الوثيقة:</strong>
                        <span class="text-muted">{{ document.document_date.strftime('%Y-%m-%d') }}</span>
                    </div>
                    {% endif %}
                    {% if document.received_date %}
                    <div class="col-md-6">
                        <strong>تاريخ الاستلام:</strong>
                        <span class="text-muted">{{ document.received_date.strftime('%Y-%m-%d') }}</span>
                    </div>
                    {% endif %}
                </div>

                <!-- Tags -->
                {% if document.tags %}
                <div class="mb-3">
                    <strong>الكلمات المفتاحية:</strong>
                    <div class="mt-2">
                        {% for tag in document.tags %}
                        <span class="badge bg-secondary me-1">{{ tag.tag_name }}</span>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Status Badges -->
                <div class="mb-3">
                    <strong>الحالة:</strong>
                    <div class="mt-2">
                        <span class="badge bg-{{ 'danger' if document.document_type.priority == 'urgent' else 'primary' if document.document_type.priority == 'normal' else 'secondary' }}">
                            {{ document.document_type.name_ar }}
                        </span>
                        {% if document.is_confidential %}
                        <span class="badge bg-danger ms-1">سري</span>
                        {% endif %}
                        {% if document.is_archived %}
                        <span class="badge bg-warning ms-1">مؤرشف</span>
                        {% endif %}
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="d-flex gap-2 flex-wrap">
                    <a href="{{ url_for('file_viewer', id=document.id) }}" class="btn btn-info">
                        <i class="fas fa-eye me-1"></i>عرض الملف
                    </a>

                    <a href="{{ url_for('download_document', id=document.id) }}" class="btn btn-success">
                        <i class="fas fa-download me-1"></i>تحميل الملف
                    </a>

                    {% set file_ext = document.filename.split('.')[-1].lower() %}
                    {% if file_ext in ['pdf', 'txt', 'jpg', 'jpeg', 'png', 'gif', 'bmp'] %}
                        <a href="{{ url_for('print_file', id=document.id) }}" class="btn btn-warning" target="_blank">
                            <i class="fas fa-print me-1"></i>طباعة الملف
                        </a>
                    {% endif %}

                    {% if current_user.role == 'admin' or document.uploaded_by == current_user.id %}
                    <a href="{{ url_for('edit_document', id=document.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-1"></i>تعديل
                    </a>
                    {% endif %}

                    <a href="{{ url_for('search_documents') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-1"></i>العودة للبحث
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- File Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الملف
                </h5>
            </div>
            <div class="card-body">
                <div class="metadata-item">
                    <div class="metadata-label">اسم الملف الأصلي:</div>
                    <div class="metadata-value">{{ document.original_filename }}</div>
                </div>

                <div class="metadata-item">
                    <div class="metadata-label">نوع الملف:</div>
                    <div class="metadata-value">{{ document.file_type.upper() }}</div>
                </div>

                <div class="metadata-item">
                    <div class="metadata-label">حجم الملف:</div>
                    <div class="metadata-value">{{ format_file_size(document.file_size) }}</div>
                </div>

                <div class="metadata-item">
                    <div class="metadata-label">التصنيف:</div>
                    <div class="metadata-value">{{ document.category.name_ar }}</div>
                </div>

                <div class="metadata-item">
                    <div class="metadata-label">رفع بواسطة:</div>
                    <div class="metadata-value">{{ document.uploader.full_name }}</div>
                </div>

                {% if document.updated_at != document.created_at %}
                <div class="metadata-item">
                    <div class="metadata-label">آخر تحديث:</div>
                    <div class="metadata-value">{{ document.updated_at.strftime('%Y-%m-%d %H:%M') }}</div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('upload_document') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>رفع وثيقة جديدة
                    </a>

                    <a href="{{ url_for('search_documents') }}?category_id={{ document.category_id }}" class="btn btn-info btn-sm">
                        <i class="fas fa-folder me-1"></i>وثائق من نفس التصنيف
                    </a>

                    <a href="{{ url_for('search_documents') }}?document_type_id={{ document.document_type_id }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-tag me-1"></i>وثائق من نفس النوع
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .metadata-item {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
    }

    .metadata-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.25rem;
        font-size: 0.9rem;
    }

    .metadata-value {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .breadcrumb {
        background-color: transparent;
        padding: 0;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
    }
</style>
{% endblock %}
