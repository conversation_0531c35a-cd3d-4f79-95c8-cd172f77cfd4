from flask_wtf import <PERSON><PERSON><PERSON>Form
from flask_wtf.file import <PERSON><PERSON>ield, FileRequired, FileAllowed
from wtforms import StringField, TextAreaField, SelectField, DateField, BooleanField, PasswordField, SubmitField, HiddenField
from wtforms.validators import <PERSON><PERSON>e<PERSON>d, Length, Email, EqualTo, Optional, Regexp
from wtforms.widgets import TextArea
from datetime import date

def coerce_int_or_none(value):
    """Convert to int or return None for empty values"""
    if value == '' or value is None:
        return None
    try:
        return int(value)
    except (ValueError, TypeError):
        return None

def flexible_email_validator():
    """Create a flexible email validator that works with or without email-validator"""
    try:
        # Try to use the standard Email validator
        return Email(message='البريد الإلكتروني غير صحيح')
    except:
        # Fallback to regex validation if email-validator is not available
        import re
        email_regex = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        return Regexp(email_regex, message='البريد الإلكتروني غير صحيح')

class LoginForm(FlaskForm):
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=3, max=80)])
    password = PasswordField('كلمة المرور', validators=[DataRequired()])
    remember_me = BooleanField('تذكرني')
    submit = SubmitField('تسجيل الدخول')

class DocumentUploadForm(FlaskForm):
    title = StringField('عنوان الوثيقة', validators=[DataRequired(), Length(max=200)])
    description = TextAreaField('الوصف', validators=[Optional(), Length(max=1000)])
    sender = StringField('المرسل', validators=[Optional(), Length(max=100)])
    receiver = StringField('المستقبل', validators=[Optional(), Length(max=100)])
    subject = StringField('الموضوع', validators=[Optional(), Length(max=200)])

    # Category selection options
    category_selection = SelectField('طريقة اختيار التصنيف', choices=[
        ('existing', 'اختيار من القائمة'),
        ('new', 'إضافة تصنيف جديد')
    ], default='existing')
    category_id = SelectField('التصنيف الموجود', coerce=coerce_int_or_none, validators=[Optional()])
    new_category_ar = StringField('التصنيف الجديد (عربي)', validators=[Optional(), Length(max=100)])
    new_category_en = StringField('التصنيف الجديد (إنجليزي)', validators=[Optional(), Length(max=100)])

    # Document type selection options
    document_type_selection = SelectField('طريقة اختيار نوع الوثيقة', choices=[
        ('existing', 'اختيار من القائمة'),
        ('new', 'إضافة نوع جديد')
    ], default='existing')
    document_type_id = SelectField('نوع الوثيقة الموجود', coerce=coerce_int_or_none, validators=[Optional()])
    new_document_type_ar = StringField('نوع الوثيقة الجديد (عربي)', validators=[Optional(), Length(max=50)])
    new_document_type_en = StringField('نوع الوثيقة الجديد (إنجليزي)', validators=[Optional(), Length(max=50)])
    new_document_type_priority = SelectField('أولوية النوع الجديد', choices=[
        ('normal', 'عادية'),
        ('urgent', 'عاجلة'),
        ('low', 'منخفضة')
    ], default='normal')

    # Document direction and registry
    direction = SelectField('اتجاه الوثيقة', choices=[
        ('internal', 'داخلية'),
        ('incoming', 'واردة'),
        ('outgoing', 'صادرة')
    ], default='internal')

    registry_type = SelectField('نوع السجل', choices=[
        ('general', 'الأرشيف العام'),
        ('incoming', 'سجل الوارد'),
        ('outgoing', 'سجل الصادر')
    ], default='general')

    document_date = DateField('تاريخ الوثيقة', validators=[Optional()], default=date.today)
    received_date = DateField('تاريخ الاستلام', validators=[Optional()], default=date.today)

    file = FileField('الملف', validators=[
        FileRequired(),
        FileAllowed(['pdf', 'doc', 'docx', 'txt', 'png', 'jpg', 'jpeg', 'gif', 'xlsx', 'xls'],
                   'الملفات المسموحة: PDF, Word, Excel, الصور, النصوص')
    ])

    is_confidential = BooleanField('وثيقة سرية')
    tags = StringField('الكلمات المفتاحية (مفصولة بفواصل)', validators=[Optional()])

    submit = SubmitField('رفع الوثيقة')

class DocumentEditForm(FlaskForm):
    title = StringField('عنوان الوثيقة', validators=[DataRequired(), Length(max=200)])
    description = TextAreaField('الوصف', validators=[Optional(), Length(max=1000)])
    sender = StringField('المرسل', validators=[Optional(), Length(max=100)])
    receiver = StringField('المستقبل', validators=[Optional(), Length(max=100)])
    subject = StringField('الموضوع', validators=[Optional(), Length(max=200)])

    category_id = SelectField('التصنيف', coerce=int, validators=[DataRequired()])
    document_type_id = SelectField('نوع الوثيقة', coerce=int, validators=[DataRequired()])

    # Document direction and registry
    direction = SelectField('اتجاه الوثيقة', choices=[
        ('internal', 'داخلية'),
        ('incoming', 'واردة'),
        ('outgoing', 'صادرة')
    ], default='internal')

    registry_type = SelectField('نوع السجل', choices=[
        ('general', 'الأرشيف العام'),
        ('incoming', 'سجل الوارد'),
        ('outgoing', 'سجل الصادر')
    ], default='general')

    document_date = DateField('تاريخ الوثيقة', validators=[Optional()])
    received_date = DateField('تاريخ الاستلام', validators=[Optional()])

    # File replacement (optional)
    file = FileField('استبدال الملف (اختياري)', validators=[
        Optional(),
        FileAllowed(['pdf', 'doc', 'docx', 'txt', 'png', 'jpg', 'jpeg', 'gif', 'xlsx', 'xls'],
                   'الملفات المسموحة: PDF, Word, Excel, الصور, النصوص')
    ])

    is_confidential = BooleanField('وثيقة سرية')
    is_archived = BooleanField('مؤرشفة')
    tags = StringField('الكلمات المفتاحية (مفصولة بفواصل)', validators=[Optional()])

    submit = SubmitField('حفظ التغييرات')

class CategoryForm(FlaskForm):
    name_ar = StringField('الاسم بالعربية', validators=[DataRequired(), Length(max=100)])
    name_en = StringField('الاسم بالإنجليزية', validators=[DataRequired(), Length(max=100)])
    description = TextAreaField('الوصف', validators=[Optional(), Length(max=500)])
    parent_id = SelectField('التصنيف الأب', coerce=coerce_int_or_none, validators=[Optional()])
    is_active = BooleanField('نشط', default=True)
    submit = SubmitField('حفظ')

class DocumentTypeForm(FlaskForm):
    name_ar = StringField('الاسم بالعربية', validators=[DataRequired(), Length(max=50)])
    name_en = StringField('الاسم بالإنجليزية', validators=[DataRequired(), Length(max=50)])
    priority = SelectField('الأولوية', choices=[
        ('normal', 'عادية'),
        ('urgent', 'عاجلة'),
        ('low', 'منخفضة')
    ], default='normal')
    is_confidential = BooleanField('سري', default=False)
    submit = SubmitField('حفظ')

class SearchForm(FlaskForm):
    query = StringField('البحث', validators=[Optional()])
    category_id = SelectField('التصنيف', coerce=coerce_int_or_none, validators=[Optional()])
    document_type_id = SelectField('نوع الوثيقة', coerce=coerce_int_or_none, validators=[Optional()])
    sender = StringField('المرسل', validators=[Optional()])
    receiver = StringField('المستقبل', validators=[Optional()])

    date_from = DateField('من تاريخ', validators=[Optional()])
    date_to = DateField('إلى تاريخ', validators=[Optional()])

    is_confidential = SelectField('السرية', choices=[('', 'الكل'), ('1', 'سري'), ('0', 'غير سري')], validators=[Optional()])
    is_archived = SelectField('الحالة', choices=[('', 'الكل'), ('1', 'مؤرشف'), ('0', 'نشط')], validators=[Optional()])

    # Registry and direction filters
    direction = SelectField('اتجاه الوثيقة', choices=[
        ('', 'الكل'),
        ('internal', 'داخلية'),
        ('incoming', 'واردة'),
        ('outgoing', 'صادرة')
    ], validators=[Optional()])

    registry_type = SelectField('نوع السجل', choices=[
        ('', 'الكل'),
        ('general', 'الأرشيف العام'),
        ('incoming', 'سجل الوارد'),
        ('outgoing', 'سجل الصادر')
    ], validators=[Optional()])

    submit = SubmitField('بحث')

class CommentForm(FlaskForm):
    comment = TextAreaField('التعليق', validators=[DataRequired(), Length(max=1000)], widget=TextArea())
    submit = SubmitField('إضافة تعليق')

class UserForm(FlaskForm):
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=3, max=80)])
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), flexible_email_validator(), Length(max=120)])
    full_name = StringField('الاسم الكامل', validators=[DataRequired(), Length(max=100)])
    role = SelectField('الدور', choices=[
        ('viewer', 'مشاهد'),
        ('employee', 'موظف'),
        ('admin', 'مدير')
    ], validators=[DataRequired()])
    is_active = BooleanField('نشط')
    password = PasswordField('كلمة المرور', validators=[Optional(), Length(min=6)])
    confirm_password = PasswordField('تأكيد كلمة المرور',
                                   validators=[EqualTo('password', message='كلمات المرور غير متطابقة')])
    submit = SubmitField('حفظ')

class CategoryForm(FlaskForm):
    name_ar = StringField('الاسم بالعربية', validators=[DataRequired(), Length(max=100)])
    name_en = StringField('الاسم بالإنجليزية', validators=[DataRequired(), Length(max=100)])
    description = TextAreaField('الوصف', validators=[Optional(), Length(max=500)])
    parent_id = SelectField('التصنيف الأب', coerce=coerce_int_or_none, validators=[Optional()])
    is_active = BooleanField('نشط', default=True)
    submit = SubmitField('حفظ')

class DocumentTypeForm(FlaskForm):
    name_ar = StringField('الاسم بالعربية', validators=[DataRequired(), Length(max=50)])
    name_en = StringField('الاسم بالإنجليزية', validators=[DataRequired(), Length(max=50)])
    priority = SelectField('الأولوية', choices=[
        ('low', 'منخفضة'),
        ('normal', 'عادية'),
        ('urgent', 'عاجلة')
    ], default='normal')
    is_confidential = BooleanField('سري بشكل افتراضي')
    submit = SubmitField('حفظ')

class ChangePasswordForm(FlaskForm):
    current_password = PasswordField('كلمة المرور الحالية', validators=[DataRequired()])
    new_password = PasswordField('كلمة المرور الجديدة', validators=[DataRequired(), Length(min=6)])
    confirm_password = PasswordField('تأكيد كلمة المرور الجديدة',
                                   validators=[DataRequired(), EqualTo('new_password', message='كلمات المرور غير متطابقة')])
    submit = SubmitField('تغيير كلمة المرور')

class ProfileForm(FlaskForm):
    full_name = StringField('الاسم الكامل', validators=[DataRequired(), Length(max=100)])
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Length(max=120)])
    avatar = SelectField('الصورة الشخصية', choices=[], validators=[Optional()])
    submit = SubmitField('حفظ التغييرات')
