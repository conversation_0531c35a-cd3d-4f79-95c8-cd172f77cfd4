from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileRequired, FileAllowed
from wtforms import StringField, TextAreaField, SelectField, DateField, BooleanField, PasswordField, SubmitField, HiddenField
from wtforms.validators import DataRequired, Length, Email, EqualTo, Optional
from wtforms.widgets import TextArea
from datetime import date

class LoginForm(FlaskForm):
    username = <PERSON><PERSON><PERSON>('اسم المستخدم', validators=[DataRequired(), Length(min=3, max=80)])
    password = PasswordField('كلمة المرور', validators=[DataRequired()])
    remember_me = BooleanField('تذكرني')
    submit = SubmitField('تسجيل الدخول')

class DocumentUploadForm(FlaskForm):
    title = StringField('عنوان الوثيقة', validators=[DataRequired(), Length(max=200)])
    description = TextAreaField('الوصف', validators=[Optional(), Length(max=1000)])
    sender = StringField('المرسل', validators=[Optional(), Length(max=100)])
    receiver = StringField('المستقبل', validators=[Optional(), Length(max=100)])
    subject = StringField('الموضوع', validators=[Optional(), Length(max=200)])
    
    category_id = SelectField('التصنيف', coerce=int, validators=[DataRequired()])
    document_type_id = SelectField('نوع الوثيقة', coerce=int, validators=[DataRequired()])
    
    document_date = DateField('تاريخ الوثيقة', validators=[Optional()], default=date.today)
    received_date = DateField('تاريخ الاستلام', validators=[Optional()], default=date.today)
    
    file = FileField('الملف', validators=[
        FileRequired(),
        FileAllowed(['pdf', 'doc', 'docx', 'txt', 'png', 'jpg', 'jpeg', 'gif', 'xlsx', 'xls'], 
                   'الملفات المسموحة: PDF, Word, Excel, الصور, النصوص')
    ])
    
    is_confidential = BooleanField('وثيقة سرية')
    tags = StringField('الكلمات المفتاحية (مفصولة بفواصل)', validators=[Optional()])
    
    submit = SubmitField('رفع الوثيقة')

class DocumentEditForm(FlaskForm):
    title = StringField('عنوان الوثيقة', validators=[DataRequired(), Length(max=200)])
    description = TextAreaField('الوصف', validators=[Optional(), Length(max=1000)])
    sender = StringField('المرسل', validators=[Optional(), Length(max=100)])
    receiver = StringField('المستقبل', validators=[Optional(), Length(max=100)])
    subject = StringField('الموضوع', validators=[Optional(), Length(max=200)])
    
    category_id = SelectField('التصنيف', coerce=int, validators=[DataRequired()])
    document_type_id = SelectField('نوع الوثيقة', coerce=int, validators=[DataRequired()])
    
    document_date = DateField('تاريخ الوثيقة', validators=[Optional()])
    received_date = DateField('تاريخ الاستلام', validators=[Optional()])
    
    is_confidential = BooleanField('وثيقة سرية')
    is_archived = BooleanField('مؤرشفة')
    tags = StringField('الكلمات المفتاحية (مفصولة بفواصل)', validators=[Optional()])
    
    submit = SubmitField('حفظ التغييرات')

class SearchForm(FlaskForm):
    query = StringField('البحث', validators=[Optional()])
    category_id = SelectField('التصنيف', coerce=int, validators=[Optional()])
    document_type_id = SelectField('نوع الوثيقة', coerce=int, validators=[Optional()])
    sender = StringField('المرسل', validators=[Optional()])
    receiver = StringField('المستقبل', validators=[Optional()])
    
    date_from = DateField('من تاريخ', validators=[Optional()])
    date_to = DateField('إلى تاريخ', validators=[Optional()])
    
    is_confidential = SelectField('السرية', choices=[('', 'الكل'), ('1', 'سري'), ('0', 'غير سري')], validators=[Optional()])
    is_archived = SelectField('الحالة', choices=[('', 'الكل'), ('1', 'مؤرشف'), ('0', 'نشط')], validators=[Optional()])
    
    submit = SubmitField('بحث')

class CommentForm(FlaskForm):
    comment = TextAreaField('التعليق', validators=[DataRequired(), Length(max=1000)], widget=TextArea())
    submit = SubmitField('إضافة تعليق')

class UserForm(FlaskForm):
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=3, max=80)])
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email(), Length(max=120)])
    full_name = StringField('الاسم الكامل', validators=[DataRequired(), Length(max=100)])
    role = SelectField('الدور', choices=[
        ('viewer', 'مشاهد'),
        ('employee', 'موظف'),
        ('admin', 'مدير')
    ], validators=[DataRequired()])
    is_active = BooleanField('نشط')
    password = PasswordField('كلمة المرور', validators=[Optional(), Length(min=6)])
    confirm_password = PasswordField('تأكيد كلمة المرور', 
                                   validators=[EqualTo('password', message='كلمات المرور غير متطابقة')])
    submit = SubmitField('حفظ')

class CategoryForm(FlaskForm):
    name_ar = StringField('الاسم بالعربية', validators=[DataRequired(), Length(max=100)])
    name_en = StringField('الاسم بالإنجليزية', validators=[DataRequired(), Length(max=100)])
    description = TextAreaField('الوصف', validators=[Optional(), Length(max=500)])
    parent_id = SelectField('التصنيف الأب', coerce=int, validators=[Optional()])
    is_active = BooleanField('نشط', default=True)
    submit = SubmitField('حفظ')

class DocumentTypeForm(FlaskForm):
    name_ar = StringField('الاسم بالعربية', validators=[DataRequired(), Length(max=50)])
    name_en = StringField('الاسم بالإنجليزية', validators=[DataRequired(), Length(max=50)])
    priority = SelectField('الأولوية', choices=[
        ('low', 'منخفضة'),
        ('normal', 'عادية'),
        ('urgent', 'عاجلة')
    ], default='normal')
    is_confidential = BooleanField('سري بشكل افتراضي')
    submit = SubmitField('حفظ')

class ChangePasswordForm(FlaskForm):
    current_password = PasswordField('كلمة المرور الحالية', validators=[DataRequired()])
    new_password = PasswordField('كلمة المرور الجديدة', validators=[DataRequired(), Length(min=6)])
    confirm_password = PasswordField('تأكيد كلمة المرور الجديدة', 
                                   validators=[DataRequired(), EqualTo('new_password', message='كلمات المرور غير متطابقة')])
    submit = SubmitField('تغيير كلمة المرور')
