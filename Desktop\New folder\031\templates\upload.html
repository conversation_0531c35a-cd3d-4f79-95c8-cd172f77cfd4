{% extends "base.html" %}

{% block title %}رفع وثيقة جديدة - نظام الأرشيف الإلكتروني{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-upload me-2"></i>
            رفع وثيقة جديدة
        </h1>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-plus me-2"></i>
                    معلومات الوثيقة
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="upload-form" class="auto-save-form">
                    {{ form.hidden_tag() }}

                    <!-- File Upload Area -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">{{ form.file.label.text }}</label>
                        <div class="upload-area" id="upload-area">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <h5>اسحب الملف هنا أو انقر للاختيار</h5>
                            <p class="text-muted">الملفات المدعومة: PDF, Word, Excel, الصور</p>
                            <p class="text-muted small">الحد الأقصى لحجم الملف: 50 ميجابايت</p>
                        </div>
                        {{ form.file(class="form-control d-none", id="file") }}
                        {% if form.file.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.file.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Basic Information -->
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            {{ form.title.label(class="form-label fw-bold") }}
                            {{ form.title(class="form-control") }}
                            {% if form.title.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.title.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Category Selection -->
                    <div class="mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-folder me-2"></i>التصنيف
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    {{ form.category_selection.label(class="form-label fw-bold") }}
                                    {{ form.category_selection(class="form-select", id="category_selection") }}
                                </div>

                                <div id="existing_category_section">
                                    {{ form.category_id.label(class="form-label fw-bold") }}
                                    {{ form.category_id(class="form-select") }}
                                    {% if form.category_id.errors %}
                                        <div class="text-danger small mt-1">
                                            {% for error in form.category_id.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div id="new_category_section" style="display: none;">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            {{ form.new_category_ar.label(class="form-label fw-bold") }}
                                            {{ form.new_category_ar(class="form-control", placeholder="مثال: الوثائق الإدارية") }}
                                            {% if form.new_category_ar.errors %}
                                                <div class="text-danger small mt-1">
                                                    {% for error in form.new_category_ar.errors %}
                                                        <div>{{ error }}</div>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            {{ form.new_category_en.label(class="form-label fw-bold") }}
                                            {{ form.new_category_en(class="form-control", placeholder="Example: Administrative Documents") }}
                                            {% if form.new_category_en.errors %}
                                                <div class="text-danger small mt-1">
                                                    {% for error in form.new_category_en.errors %}
                                                        <div>{{ error }}</div>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-1"></i>
                                        سيتم إنشاء تصنيف جديد إذا لم يكن موجوداً، أو استخدام الموجود إذا كان متطابقاً
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Document Type Selection -->
                    <div class="mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-file-alt me-2"></i>نوع الوثيقة
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    {{ form.document_type_selection.label(class="form-label fw-bold") }}
                                    {{ form.document_type_selection(class="form-select", id="document_type_selection") }}
                                </div>

                                <div id="existing_document_type_section">
                                    {{ form.document_type_id.label(class="form-label fw-bold") }}
                                    {{ form.document_type_id(class="form-select") }}
                                    {% if form.document_type_id.errors %}
                                        <div class="text-danger small mt-1">
                                            {% for error in form.document_type_id.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div id="new_document_type_section" style="display: none;">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            {{ form.new_document_type_ar.label(class="form-label fw-bold") }}
                                            {{ form.new_document_type_ar(class="form-control", placeholder="مثال: مذكرة داخلية") }}
                                            {% if form.new_document_type_ar.errors %}
                                                <div class="text-danger small mt-1">
                                                    {% for error in form.new_document_type_ar.errors %}
                                                        <div>{{ error }}</div>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            {{ form.new_document_type_en.label(class="form-label fw-bold") }}
                                            {{ form.new_document_type_en(class="form-control", placeholder="Example: Internal Memo") }}
                                            {% if form.new_document_type_en.errors %}
                                                <div class="text-danger small mt-1">
                                                    {% for error in form.new_document_type_en.errors %}
                                                        <div>{{ error }}</div>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        {{ form.new_document_type_priority.label(class="form-label fw-bold") }}
                                        {{ form.new_document_type_priority(class="form-select") }}
                                    </div>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-1"></i>
                                        سيتم إنشاء نوع وثيقة جديد إذا لم يكن موجوداً، أو استخدام الموجود إذا كان متطابقاً
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control", rows="3") }}
                        {% if form.description.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.description.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Correspondence Information -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.sender.label(class="form-label") }}
                            {{ form.sender(class="form-control") }}
                            {% if form.sender.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.sender.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.receiver.label(class="form-label") }}
                            {{ form.receiver(class="form-control") }}
                            {% if form.receiver.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.receiver.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.subject.label(class="form-label") }}
                        {{ form.subject(class="form-control") }}
                        {% if form.subject.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.subject.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Dates -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.document_date.label(class="form-label") }}
                            {{ form.document_date(class="form-control") }}
                            {% if form.document_date.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.document_date.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.received_date.label(class="form-label") }}
                            {{ form.received_date(class="form-control") }}
                            {% if form.received_date.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.received_date.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Tags and Options -->
                    <div class="mb-3">
                        {{ form.tags.label(class="form-label") }}
                        {{ form.tags(class="form-control", placeholder="مثال: مالية, عقود, مهم") }}
                        <div class="form-text">أدخل الكلمات المفتاحية مفصولة بفواصل</div>
                        {% if form.tags.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.tags.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-4">
                        <div class="form-check">
                            {{ form.is_confidential(class="form-check-input") }}
                            {{ form.is_confidential.label(class="form-check-label") }}
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('dashboard') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Help Sidebar -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    إرشادات الرفع
                </h5>
            </div>
            <div class="card-body">
                <h6 class="fw-bold">الملفات المدعومة:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-file-pdf text-danger me-2"></i>PDF</li>
                    <li><i class="fas fa-file-word text-primary me-2"></i>Word (DOC, DOCX)</li>
                    <li><i class="fas fa-file-excel text-success me-2"></i>Excel (XLS, XLSX)</li>
                    <li><i class="fas fa-file-image text-info me-2"></i>الصور (PNG, JPG, GIF)</li>
                    <li><i class="fas fa-file-alt text-secondary me-2"></i>النصوص (TXT)</li>
                </ul>

                <hr>

                <h6 class="fw-bold">نصائح مهمة:</h6>
                <ul class="small">
                    <li>تأكد من وضوح عنوان الوثيقة</li>
                    <li>اختر التصنيف المناسب أو أنشئ جديد</li>
                    <li>حدد نوع الوثيقة أو أضف نوع جديد</li>
                    <li>أضف وصفاً مفصلاً للوثيقة</li>
                    <li>استخدم الكلمات المفتاحية لتسهيل البحث</li>
                    <li>تأكد من صحة التواريخ</li>
                </ul>

                <div class="alert alert-success small">
                    <i class="fas fa-plus-circle me-1"></i>
                    <strong>جديد!</strong> يمكنك الآن إنشاء تصنيفات وأنواع وثائق جديدة أثناء الرفع
                </div>

                <hr>

                <h6 class="fw-bold">الحد الأقصى لحجم الملف:</h6>
                <p class="small text-muted">50 ميجابايت</p>

                <div class="alert alert-info small">
                    <i class="fas fa-lightbulb me-1"></i>
                    <strong>تلميح:</strong> يمكنك سحب الملف وإفلاته مباشرة في منطقة الرفع
                </div>
            </div>
        </div>

        <!-- Auto-save indicator -->
        <div class="auto-save-indicator position-fixed bottom-0 end-0 m-3 alert alert-success" style="display: none;">
            <i class="fas fa-check me-1"></i>تم الحفظ التلقائي
        </div>
    </div>
</div>

<!-- Loading Spinner -->
<div class="loading-spinner">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">جاري التحميل...</span>
    </div>
    <p class="mt-2">جاري رفع الوثيقة...</p>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Load saved form data if exists
    ArchiveSystem.loadFormData('upload-form');

    // Show loading on form submit
    $('#upload-form').on('submit', function() {
        ArchiveSystem.showLoading();
    });

    // Clear saved data on successful submit
    {% if request.method == 'POST' and not form.errors %}
        ArchiveSystem.clearFormData('upload-form');
    {% endif %}

    // Category selection toggle
    $('#category_selection').on('change', function() {
        const selection = $(this).val();
        if (selection === 'new') {
            $('#existing_category_section').hide();
            $('#new_category_section').show();
            // Clear existing category selection
            $('#category_id').val('');
        } else {
            $('#existing_category_section').show();
            $('#new_category_section').hide();
            // Clear new category fields
            $('#new_category_ar').val('');
            $('#new_category_en').val('');
        }
    });

    // Document type selection toggle
    $('#document_type_selection').on('change', function() {
        const selection = $(this).val();
        if (selection === 'new') {
            $('#existing_document_type_section').hide();
            $('#new_document_type_section').show();
            // Clear existing document type selection
            $('#document_type_id').val('');
        } else {
            $('#existing_document_type_section').show();
            $('#new_document_type_section').hide();
            // Clear new document type fields
            $('#new_document_type_ar').val('');
            $('#new_document_type_en').val('');
            $('#new_document_type_priority').val('normal');
        }
    });

    // Initialize sections based on current selection
    $('#category_selection').trigger('change');
    $('#document_type_selection').trigger('change');

    // Auto-fill English name based on Arabic (optional helper)
    $('#new_category_ar').on('input', function() {
        const arabicText = $(this).val();
        if (arabicText && !$('#new_category_en').val()) {
            // Simple transliteration suggestions (you can enhance this)
            const suggestions = {
                'الوثائق الإدارية': 'Administrative Documents',
                'المراسلات': 'Correspondence',
                'العقود': 'Contracts',
                'التقارير': 'Reports',
                'المالية': 'Financial',
                'الموارد البشرية': 'Human Resources'
            };

            if (suggestions[arabicText]) {
                $('#new_category_en').val(suggestions[arabicText]);
            }
        }
    });

    $('#new_document_type_ar').on('input', function() {
        const arabicText = $(this).val();
        if (arabicText && !$('#new_document_type_en').val()) {
            // Simple transliteration suggestions
            const suggestions = {
                'مذكرة داخلية': 'Internal Memo',
                'خطاب رسمي': 'Official Letter',
                'تقرير': 'Report',
                'عقد': 'Contract',
                'فاتورة': 'Invoice',
                'شهادة': 'Certificate'
            };

            if (suggestions[arabicText]) {
                $('#new_document_type_en').val(suggestions[arabicText]);
            }
        }
    });

    // Form validation enhancement
    $('#upload-form').on('submit', function(e) {
        let isValid = true;
        let errorMessage = '';

        // Check category selection
        if ($('#category_selection').val() === 'existing') {
            if (!$('#category_id').val()) {
                isValid = false;
                errorMessage += 'يرجى اختيار التصنيف\n';
            }
        } else if ($('#category_selection').val() === 'new') {
            if (!$('#new_category_ar').val() || !$('#new_category_en').val()) {
                isValid = false;
                errorMessage += 'يرجى إدخال اسم التصنيف باللغتين العربية والإنجليزية\n';
            }
        }

        // Check document type selection
        if ($('#document_type_selection').val() === 'existing') {
            if (!$('#document_type_id').val()) {
                isValid = false;
                errorMessage += 'يرجى اختيار نوع الوثيقة\n';
            }
        } else if ($('#document_type_selection').val() === 'new') {
            if (!$('#new_document_type_ar').val() || !$('#new_document_type_en').val()) {
                isValid = false;
                errorMessage += 'يرجى إدخال اسم نوع الوثيقة باللغتين العربية والإنجليزية\n';
            }
        }

        if (!isValid) {
            e.preventDefault();
            alert(errorMessage);
            return false;
        }

        ArchiveSystem.showLoading();
    });
});
</script>
{% endblock %}
