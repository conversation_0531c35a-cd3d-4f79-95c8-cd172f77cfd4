/**
 * Document Scanner Module
 * Provides camera access and document scanning functionality
 */

class DocumentScanner {
    constructor() {
        this.stream = null;
        this.video = null;
        this.canvas = null;
        this.context = null;
        this.isScanning = false;
        this.capturedImages = [];
        this.currentDeviceId = null;
        this.devices = [];
    }

    /**
     * Initialize the scanner
     */
    async init() {
        try {
            // Get available camera devices
            await this.getDevices();

            // Create scanner elements
            this.createScannerElements();

            // Setup event listeners
            this.setupEventListeners();

            console.log('📷 Document scanner initialized successfully');
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize scanner:', error);
            this.showError('فشل في تهيئة الماسح الضوئي: ' + error.message);
            return false;
        }
    }

    /**
     * Get available camera devices
     */
    async getDevices() {
        try {
            const devices = await navigator.mediaDevices.enumerateDevices();
            this.devices = devices.filter(device => device.kind === 'videoinput');

            if (this.devices.length === 0) {
                throw new Error('لم يتم العثور على كاميرا متاحة');
            }

            // Prefer back camera if available
            const backCamera = this.devices.find(device =>
                device.label.toLowerCase().includes('back') ||
                device.label.toLowerCase().includes('rear')
            );

            this.currentDeviceId = backCamera ? backCamera.deviceId : this.devices[0].deviceId;

        } catch (error) {
            throw new Error('فشل في الوصول للكاميرا: ' + error.message);
        }
    }

    /**
     * Create scanner UI elements
     */
    createScannerElements() {
        const scannerHTML = `
            <div id="documentScanner" class="scanner-container" style="display: none;">
                <div class="scanner-header">
                    <h5>
                        <i class="fas fa-camera me-2"></i>
                        الماسح الضوئي للوثائق
                    </h5>
                    <button type="button" class="btn-close scanner-close" aria-label="إغلاق"></button>
                </div>

                <div class="scanner-body">
                    <!-- Camera Controls -->
                    <div class="camera-controls mb-3">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">اختيار الكاميرا:</label>
                                <select id="cameraSelect" class="form-select form-select-sm">
                                    ${this.devices.map(device =>
                                        `<option value="${device.deviceId}" ${device.deviceId === this.currentDeviceId ? 'selected' : ''}>
                                            ${device.label || 'كاميرا ' + (this.devices.indexOf(device) + 1)}
                                        </option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">الجودة:</label>
                                <select id="qualitySelect" class="form-select form-select-sm">
                                    <option value="1920x1080">عالية (1920x1080)</option>
                                    <option value="1280x720" selected>متوسطة (1280x720)</option>
                                    <option value="640x480">منخفضة (640x480)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Camera Preview -->
                    <div class="camera-preview">
                        <video id="scannerVideo" autoplay playsinline muted></video>
                        <canvas id="scannerCanvas" style="display: none;"></canvas>

                        <!-- Overlay Guide -->
                        <div class="scan-overlay">
                            <div class="scan-frame">
                                <div class="corner top-left"></div>
                                <div class="corner top-right"></div>
                                <div class="corner bottom-left"></div>
                                <div class="corner bottom-right"></div>
                            </div>
                            <div class="scan-instructions">
                                <p>ضع الوثيقة داخل الإطار واضغط على التقاط</p>
                            </div>
                        </div>
                    </div>

                    <!-- Capture Controls -->
                    <div class="capture-controls mt-3">
                        <div class="d-flex justify-content-center gap-2">
                            <button type="button" id="startScanBtn" class="btn btn-success">
                                <i class="fas fa-play me-1"></i>بدء المسح
                            </button>
                            <button type="button" id="captureBtn" class="btn btn-primary" style="display: none;">
                                <i class="fas fa-camera me-1"></i>التقاط
                            </button>
                            <button type="button" id="stopScanBtn" class="btn btn-danger" style="display: none;">
                                <i class="fas fa-stop me-1"></i>إيقاف
                            </button>
                            <button type="button" id="switchCameraBtn" class="btn btn-secondary" style="display: none;">
                                <i class="fas fa-sync-alt me-1"></i>تبديل الكاميرا
                            </button>
                        </div>
                    </div>

                    <!-- Captured Images -->
                    <div id="capturedImages" class="captured-images mt-4" style="display: none;">
                        <h6>الصور الملتقطة:</h6>
                        <div class="images-grid" id="imagesGrid"></div>
                        <div class="images-actions mt-3">
                            <button type="button" id="useImagesBtn" class="btn btn-success">
                                <i class="fas fa-check me-1"></i>استخدام الصور
                            </button>
                            <button type="button" id="clearImagesBtn" class="btn btn-outline-danger">
                                <i class="fas fa-trash me-1"></i>مسح الكل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add scanner to page
        document.body.insertAdjacentHTML('beforeend', scannerHTML);

        // Get references
        this.video = document.getElementById('scannerVideo');
        this.canvas = document.getElementById('scannerCanvas');
        this.context = this.canvas.getContext('2d');
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Close scanner
        document.querySelector('.scanner-close').addEventListener('click', () => {
            this.close();
        });

        // Start scanning
        document.getElementById('startScanBtn').addEventListener('click', () => {
            this.startScanning();
        });

        // Capture image
        document.getElementById('captureBtn').addEventListener('click', () => {
            this.captureImage();
        });

        // Stop scanning
        document.getElementById('stopScanBtn').addEventListener('click', () => {
            this.stopScanning();
        });

        // Switch camera
        document.getElementById('switchCameraBtn').addEventListener('click', () => {
            this.switchCamera();
        });

        // Camera selection change
        document.getElementById('cameraSelect').addEventListener('change', (e) => {
            this.currentDeviceId = e.target.value;
            if (this.isScanning) {
                this.stopScanning();
                setTimeout(() => this.startScanning(), 500);
            }
        });

        // Use captured images
        document.getElementById('useImagesBtn').addEventListener('click', () => {
            this.useImages();
        });

        // Clear captured images
        document.getElementById('clearImagesBtn').addEventListener('click', () => {
            this.clearImages();
        });

        // ESC key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen()) {
                this.close();
            }
        });
    }

    /**
     * Open scanner modal
     */
    open() {
        document.getElementById('documentScanner').style.display = 'block';
        document.body.style.overflow = 'hidden';
    }

    /**
     * Close scanner modal
     */
    close() {
        this.stopScanning();
        document.getElementById('documentScanner').style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    /**
     * Check if scanner is open
     */
    isOpen() {
        return document.getElementById('documentScanner').style.display === 'block';
    }

    /**
     * Start camera scanning
     */
    async startScanning() {
        try {
            const quality = document.getElementById('qualitySelect').value;
            const [width, height] = quality.split('x').map(Number);

            const constraints = {
                video: {
                    deviceId: this.currentDeviceId ? { exact: this.currentDeviceId } : undefined,
                    width: { ideal: width },
                    height: { ideal: height },
                    facingMode: 'environment' // Prefer back camera
                }
            };

            this.stream = await navigator.mediaDevices.getUserMedia(constraints);
            this.video.srcObject = this.stream;

            this.video.onloadedmetadata = () => {
                this.canvas.width = this.video.videoWidth;
                this.canvas.height = this.video.videoHeight;
            };

            this.isScanning = true;
            this.updateUI();

        } catch (error) {
            console.error('❌ Failed to start camera:', error);
            this.showError('فشل في تشغيل الكاميرا: ' + error.message);
        }
    }

    /**
     * Stop camera scanning
     */
    stopScanning() {
        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            this.stream = null;
        }

        this.isScanning = false;
        this.updateUI();
    }

    /**
     * Capture image from video
     */
    captureImage() {
        if (!this.isScanning) return;

        try {
            // Draw video frame to canvas
            this.context.drawImage(this.video, 0, 0, this.canvas.width, this.canvas.height);

            // Convert to blob
            this.canvas.toBlob((blob) => {
                const timestamp = new Date().getTime();
                const filename = `scanned_document_${timestamp}.png`;

                // Create file object
                const file = new File([blob], filename, { type: 'image/png' });

                // Add to captured images
                this.capturedImages.push({
                    file: file,
                    url: URL.createObjectURL(blob),
                    timestamp: timestamp
                });

                this.updateCapturedImages();
                this.showSuccess('تم التقاط الصورة بنجاح');

            }, 'image/png', 0.9);

        } catch (error) {
            console.error('❌ Failed to capture image:', error);
            this.showError('فشل في التقاط الصورة: ' + error.message);
        }
    }

    /**
     * Switch to next available camera
     */
    async switchCamera() {
        const currentIndex = this.devices.findIndex(device => device.deviceId === this.currentDeviceId);
        const nextIndex = (currentIndex + 1) % this.devices.length;

        this.currentDeviceId = this.devices[nextIndex].deviceId;
        document.getElementById('cameraSelect').value = this.currentDeviceId;

        if (this.isScanning) {
            this.stopScanning();
            setTimeout(() => this.startScanning(), 500);
        }
    }

    /**
     * Update captured images display
     */
    updateCapturedImages() {
        const grid = document.getElementById('imagesGrid');
        const container = document.getElementById('capturedImages');

        if (this.capturedImages.length === 0) {
            container.style.display = 'none';
            return;
        }

        container.style.display = 'block';

        grid.innerHTML = this.capturedImages.map((img, index) => `
            <div class="captured-image" data-index="${index}">
                <img src="${img.url}" alt="صورة ملتقطة ${index + 1}">
                <div class="image-overlay">
                    <button type="button" class="btn btn-sm btn-danger delete-image" data-index="${index}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="image-info">
                    صورة ${index + 1}
                </div>
            </div>
        `).join('');

        // Add delete event listeners
        grid.querySelectorAll('.delete-image').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const index = parseInt(e.target.closest('.delete-image').dataset.index);
                this.deleteImage(index);
            });
        });
    }

    /**
     * Delete captured image
     */
    deleteImage(index) {
        if (index >= 0 && index < this.capturedImages.length) {
            URL.revokeObjectURL(this.capturedImages[index].url);
            this.capturedImages.splice(index, 1);
            this.updateCapturedImages();
        }
    }

    /**
     * Clear all captured images
     */
    clearImages() {
        this.capturedImages.forEach(img => URL.revokeObjectURL(img.url));
        this.capturedImages = [];
        this.updateCapturedImages();
    }

    /**
     * Use captured images in file input
     */
    useImages() {
        if (this.capturedImages.length === 0) {
            this.showError('لا توجد صور ملتقطة لاستخدامها');
            return;
        }

        // Create file list from captured images
        const dt = new DataTransfer();
        this.capturedImages.forEach(img => {
            dt.items.add(img.file);
        });

        // Set files to file input
        const fileInput = document.getElementById('file');
        if (fileInput) {
            fileInput.files = dt.files;

            // Trigger change event
            const event = new Event('change', { bubbles: true });
            fileInput.dispatchEvent(event);

            // Trigger custom event for upload form integration
            const files = Array.from(dt.files);
            $(document).trigger('scannerFilesReady', [files]);

            this.showSuccess(`تم إضافة ${this.capturedImages.length} صورة للنموذج`);
            this.close();
        } else {
            this.showError('لم يتم العثور على حقل رفع الملف');
        }
    }

    /**
     * Update UI based on current state
     */
    updateUI() {
        const startBtn = document.getElementById('startScanBtn');
        const captureBtn = document.getElementById('captureBtn');
        const stopBtn = document.getElementById('stopScanBtn');
        const switchBtn = document.getElementById('switchCameraBtn');

        if (this.isScanning) {
            startBtn.style.display = 'none';
            captureBtn.style.display = 'inline-block';
            stopBtn.style.display = 'inline-block';
            switchBtn.style.display = this.devices.length > 1 ? 'inline-block' : 'none';
        } else {
            startBtn.style.display = 'inline-block';
            captureBtn.style.display = 'none';
            stopBtn.style.display = 'none';
            switchBtn.style.display = 'none';
        }
    }

    /**
     * Show success message
     */
    showSuccess(message) {
        this.showMessage(message, 'success');
    }

    /**
     * Show error message
     */
    showError(message) {
        this.showMessage(message, 'error');
    }

    /**
     * Show message
     */
    showMessage(message, type) {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} scanner-toast`;
        toast.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            ${message}
        `;

        document.body.appendChild(toast);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }
}

// Global scanner instance
window.documentScanner = null;

// Initialize scanner when DOM is ready
document.addEventListener('DOMContentLoaded', async () => {
    // Check if browser supports required APIs
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        console.warn('⚠️ Camera API not supported in this browser');
        return;
    }

    // Initialize scanner
    window.documentScanner = new DocumentScanner();
    await window.documentScanner.init();
});
