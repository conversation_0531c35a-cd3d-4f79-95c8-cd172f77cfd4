from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_file, abort
from flask_login import Lo<PERSON><PERSON>anager, login_user, logout_user, login_required, current_user
from werkzeug.utils import secure_filename
import os
from datetime import datetime, date
from sqlalchemy import or_, and_

# Import our modules
from config import Config
from models import db, User, Document, Category, DocumentType, DocumentComment, ActivityLog, SystemSettings
from forms import (LoginForm, DocumentUploadForm, DocumentEditForm, SearchForm,
                  CommentForm, UserForm, CategoryForm, DocumentTypeForm, ChangePasswordForm)
from utils import (allowed_file, generate_unique_filename, get_file_info, organize_file_path,
                  generate_registration_number, log_activity, format_file_size, get_file_icon_class)

# Create Flask app
app = Flask(__name__)
app.config.from_object(Config)

# Initialize extensions
db.init_app(app)

# Initialize Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
login_manager.login_message_category = 'info'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Initialize configuration
Config.init_app(app)

# Routes
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()

        if user and user.check_password(form.password.data) and user.is_active:
            login_user(user, remember=form.remember_me.data)
            user.last_login = datetime.utcnow()
            db.session.commit()

            # Log login activity
            log_activity(
                user_id=user.id,
                action='login',
                description='تسجيل دخول المستخدم',
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent')
            )

            next_page = request.args.get('next')
            if not next_page or not next_page.startswith('/'):
                next_page = url_for('dashboard')
            return redirect(next_page)
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template('login.html', form=form)

@app.route('/logout')
@login_required
def logout():
    log_activity(
        user_id=current_user.id,
        action='logout',
        description='تسجيل خروج المستخدم',
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent')
    )
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    # Get statistics
    total_documents = Document.query.filter_by(status='active').count()
    my_documents = Document.query.filter_by(uploaded_by=current_user.id, status='active').count()
    archived_documents = Document.query.filter_by(is_archived=True, status='active').count()
    confidential_documents = Document.query.filter_by(is_confidential=True, status='active').count()

    # Get recent documents
    recent_documents = Document.query.filter_by(status='active').order_by(Document.created_at.desc()).limit(10).all()

    # Get recent activities
    recent_activities = ActivityLog.query.order_by(ActivityLog.created_at.desc()).limit(10).all()

    stats = {
        'total_documents': total_documents,
        'my_documents': my_documents,
        'archived_documents': archived_documents,
        'confidential_documents': confidential_documents
    }

    return render_template('dashboard.html',
                         stats=stats,
                         recent_documents=recent_documents,
                         recent_activities=recent_activities,
                         get_file_icon_class=get_file_icon_class)

@app.route('/upload', methods=['GET', 'POST'])
@login_required
def upload_document():
    if not current_user.has_permission('upload'):
        flash('ليس لديك صلاحية لرفع الوثائق', 'error')
        return redirect(url_for('dashboard'))

    form = DocumentUploadForm()

    # Populate choices for select fields
    form.category_id.choices = [('', 'اختر التصنيف')] + [(c.id, c.name_ar) for c in Category.query.filter_by(is_active=True).all()]
    form.document_type_id.choices = [('', 'اختر نوع الوثيقة')] + [(dt.id, dt.name_ar) for dt in DocumentType.query.all()]

    if form.validate_on_submit():
        file = form.file.data

        if file and allowed_file(file.filename):
            try:
                # Handle category selection/creation
                if form.category_selection.data == 'new':
                    # Create new category
                    if not form.new_category_ar.data or not form.new_category_en.data:
                        flash('يرجى إدخال اسم التصنيف باللغتين العربية والإنجليزية', 'error')
                        return render_template('upload.html', form=form)

                    # Check if category already exists
                    existing_category = Category.query.filter(
                        (Category.name_ar == form.new_category_ar.data) |
                        (Category.name_en == form.new_category_en.data)
                    ).first()

                    if existing_category:
                        category = existing_category
                        flash(f'التصنيف "{existing_category.name_ar}" موجود بالفعل، تم استخدامه', 'info')
                    else:
                        category = Category(
                            name_ar=form.new_category_ar.data,
                            name_en=form.new_category_en.data,
                            description=f'تصنيف تم إنشاؤه تلقائياً عند رفع الوثيقة'
                        )
                        db.session.add(category)
                        db.session.flush()  # Get the ID
                        flash(f'تم إنشاء تصنيف جديد: {category.name_ar}', 'success')
                else:
                    # Use existing category
                    if not form.category_id.data:
                        flash('يرجى اختيار التصنيف', 'error')
                        return render_template('upload.html', form=form)
                    category = Category.query.get(form.category_id.data)

                # Handle document type selection/creation
                if form.document_type_selection.data == 'new':
                    # Create new document type
                    if not form.new_document_type_ar.data or not form.new_document_type_en.data:
                        flash('يرجى إدخال اسم نوع الوثيقة باللغتين العربية والإنجليزية', 'error')
                        return render_template('upload.html', form=form)

                    # Check if document type already exists
                    existing_doc_type = DocumentType.query.filter(
                        (DocumentType.name_ar == form.new_document_type_ar.data) |
                        (DocumentType.name_en == form.new_document_type_en.data)
                    ).first()

                    if existing_doc_type:
                        document_type = existing_doc_type
                        flash(f'نوع الوثيقة "{existing_doc_type.name_ar}" موجود بالفعل، تم استخدامه', 'info')
                    else:
                        document_type = DocumentType(
                            name_ar=form.new_document_type_ar.data,
                            name_en=form.new_document_type_en.data,
                            priority=form.new_document_type_priority.data,
                            is_confidential=form.is_confidential.data
                        )
                        db.session.add(document_type)
                        db.session.flush()  # Get the ID
                        flash(f'تم إنشاء نوع وثيقة جديد: {document_type.name_ar}', 'success')
                else:
                    # Use existing document type
                    if not form.document_type_id.data:
                        flash('يرجى اختيار نوع الوثيقة', 'error')
                        return render_template('upload.html', form=form)
                    document_type = DocumentType.query.get(form.document_type_id.data)

                # Generate unique filename
                filename = generate_unique_filename(file.filename)

                # Get category for file organization
                file_path = organize_file_path(filename, category.name_en)
                full_path = os.path.join(app.config['UPLOAD_FOLDER'], 'documents', file_path)

                # Save file
                file.save(full_path)

                # Get file information
                file_info = get_file_info(full_path)

                # Generate registration number based on registry type
                registry_prefix = {
                    'incoming': 'IN',
                    'outgoing': 'OUT',
                    'general': 'GEN'
                }.get(form.registry_type.data, 'GEN')

                reg_number = generate_registration_number(registry_prefix)

                # Generate registry-specific number
                registry_number = generate_registry_number(form.registry_type.data)

                # Create document record
                document = Document(
                    registration_number=reg_number,
                    title=form.title.data,
                    description=form.description.data,
                    sender=form.sender.data,
                    receiver=form.receiver.data,
                    subject=form.subject.data,
                    filename=filename,
                    original_filename=file.filename,
                    file_path=file_path,
                    file_size=file_info['size'],
                    file_type=file.filename.split('.')[-1].lower(),
                    mime_type=file_info['mime_type'],
                    document_date=form.document_date.data,
                    received_date=form.received_date.data,
                    category_id=category.id,
                    document_type_id=document_type.id,
                    uploaded_by=current_user.id,
                    is_confidential=form.is_confidential.data,
                    direction=form.direction.data,
                    registry_type=form.registry_type.data,
                    registry_number=registry_number
                )

                db.session.add(document)
                db.session.commit()

                # Add tags if provided
                if form.tags.data:
                    from models import DocumentTag
                    tags = [tag.strip() for tag in form.tags.data.split(',') if tag.strip()]
                    for tag_name in tags:
                        tag = DocumentTag(document_id=document.id, tag_name=tag_name)
                        db.session.add(tag)

                db.session.commit()

                # Log activity
                log_activity(
                    user_id=current_user.id,
                    action='upload',
                    document_id=document.id,
                    description=f'رفع وثيقة جديدة: {document.title}',
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent')
                )

                flash(f'تم رفع الوثيقة بنجاح. رقم التسجيل: {reg_number}', 'success')
                return redirect(url_for('view_document', id=document.id))

            except Exception as e:
                db.session.rollback()
                flash(f'حدث خطأ أثناء رفع الوثيقة: {str(e)}', 'error')
        else:
            flash('نوع الملف غير مدعوم', 'error')

    return render_template('upload.html', form=form)

@app.route('/search')
@login_required
def search_documents():
    form = SearchForm()

    # Populate choices
    form.category_id.choices = [('', 'جميع التصنيفات')] + [(c.id, c.name_ar) for c in Category.query.filter_by(is_active=True).all()]
    form.document_type_id.choices = [('', 'جميع الأنواع')] + [(dt.id, dt.name_ar) for dt in DocumentType.query.all()]

    documents = []
    page = request.args.get('page', 1, type=int)

    if request.args.get('query') or any(request.args.get(field) for field in ['category_id', 'document_type_id', 'sender', 'receiver', 'date_from', 'date_to']):
        # Build query
        query = Document.query.filter_by(status='active')

        # Text search
        if request.args.get('query'):
            search_term = f"%{request.args.get('query')}%"
            query = query.filter(
                or_(
                    Document.title.like(search_term),
                    Document.description.like(search_term),
                    Document.subject.like(search_term),
                    Document.sender.like(search_term),
                    Document.receiver.like(search_term),
                    Document.registration_number.like(search_term)
                )
            )

        # Category filter
        if request.args.get('category_id') and request.args.get('category_id') != '':
            try:
                category_id = int(request.args.get('category_id'))
                query = query.filter_by(category_id=category_id)
            except (ValueError, TypeError):
                pass

        # Document type filter
        if request.args.get('document_type_id') and request.args.get('document_type_id') != '':
            try:
                document_type_id = int(request.args.get('document_type_id'))
                query = query.filter_by(document_type_id=document_type_id)
            except (ValueError, TypeError):
                pass

        # Check permissions for confidential documents
        if current_user.role == 'viewer':
            query = query.filter_by(is_confidential=False)

        # Paginate results
        documents = query.order_by(Document.created_at.desc()).paginate(
            page=page, per_page=app.config['DOCUMENTS_PER_PAGE'], error_out=False
        )

    return render_template('search.html', form=form, documents=documents, get_file_icon_class=get_file_icon_class)

@app.route('/document/<int:id>')
@login_required
def view_document(id):
    document = Document.query.get_or_404(id)

    # Check permissions
    if document.is_confidential and current_user.role == 'viewer':
        flash('ليس لديك صلاحية لعرض هذه الوثيقة السرية', 'error')
        return redirect(url_for('dashboard'))

    # Log view activity
    log_activity(
        user_id=current_user.id,
        action='view',
        document_id=document.id,
        description=f'عرض الوثيقة: {document.title}',
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent')
    )

    return render_template('document_view.html',
                         document=document,
                         format_file_size=format_file_size,
                         get_file_icon_class=get_file_icon_class)

@app.route('/document/<int:id>/download')
@login_required
def download_document(id):
    document = Document.query.get_or_404(id)

    # Check permissions
    if document.is_confidential and current_user.role == 'viewer':
        flash('ليس لديك صلاحية لتحميل هذه الوثيقة السرية', 'error')
        return redirect(url_for('view_document', id=id))

    file_path = os.path.join(app.config['UPLOAD_FOLDER'], 'documents', document.file_path)

    if not os.path.exists(file_path):
        flash('الملف غير موجود', 'error')
        return redirect(url_for('view_document', id=id))

    # Log download activity
    log_activity(
        user_id=current_user.id,
        action='download',
        document_id=document.id,
        description=f'تحميل الوثيقة: {document.title}',
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent')
    )

    return send_file(file_path, as_attachment=True, download_name=document.original_filename)

# Profile and User Management Routes
@app.route('/profile')
@login_required
def profile():
    return render_template('profile.html', user=current_user)

@app.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    form = ChangePasswordForm()

    if form.validate_on_submit():
        if current_user.check_password(form.current_password.data):
            current_user.set_password(form.new_password.data)
            db.session.commit()

            # Log activity
            log_activity(
                user_id=current_user.id,
                action='password_change',
                description='تغيير كلمة المرور',
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent')
            )

            flash('تم تغيير كلمة المرور بنجاح', 'success')
            return redirect(url_for('profile'))
        else:
            flash('كلمة المرور الحالية غير صحيحة', 'error')

    return render_template('change_password.html', form=form)

# Admin Routes
@app.route('/admin')
@login_required
def admin_panel():
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى لوحة الإدارة', 'error')
        return redirect(url_for('dashboard'))

    # Get statistics
    total_users = User.query.count()
    total_documents = Document.query.filter_by(status='active').count()
    total_categories = Category.query.filter_by(is_active=True).count()
    recent_activities = ActivityLog.query.order_by(ActivityLog.created_at.desc()).limit(20).all()

    stats = {
        'total_users': total_users,
        'total_documents': total_documents,
        'total_categories': total_categories
    }

    return render_template('admin.html', stats=stats, recent_activities=recent_activities)

@app.route('/admin/users')
@login_required
def manage_users():
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لإدارة المستخدمين', 'error')
        return redirect(url_for('dashboard'))

    page = request.args.get('page', 1, type=int)
    users = User.query.paginate(page=page, per_page=20, error_out=False)

    return render_template('admin_users.html', users=users)

@app.route('/admin/user/add', methods=['GET', 'POST'])
@login_required
def add_user():
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لإضافة مستخدمين', 'error')
        return redirect(url_for('dashboard'))

    form = UserForm()

    if form.validate_on_submit():
        # Check if username or email already exists
        existing_user = User.query.filter(
            (User.username == form.username.data) | (User.email == form.email.data)
        ).first()

        if existing_user:
            flash('اسم المستخدم أو البريد الإلكتروني موجود بالفعل', 'error')
        else:
            try:
                user = User(
                    username=form.username.data,
                    email=form.email.data,
                    full_name=form.full_name.data,
                    role=form.role.data,
                    is_active=form.is_active.data
                )
                user.set_password(form.password.data)

                db.session.add(user)
                db.session.commit()

                # Log activity
                log_activity(
                    user_id=current_user.id,
                    action='user_add',
                    description=f'إضافة مستخدم جديد: {user.username}',
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent')
                )

                flash('تم إضافة المستخدم بنجاح', 'success')
                return redirect(url_for('manage_users'))

            except Exception as e:
                db.session.rollback()
                flash(f'حدث خطأ أثناء إضافة المستخدم: {str(e)}', 'error')

    return render_template('admin_user_form.html', form=form, title='إضافة مستخدم جديد', action='add')

@app.route('/admin/user/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_user(id):
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لتعديل المستخدمين', 'error')
        return redirect(url_for('dashboard'))

    user = User.query.get_or_404(id)
    form = UserForm(obj=user)

    if form.validate_on_submit():
        # Check if username or email already exists (excluding current user)
        existing_user = User.query.filter(
            ((User.username == form.username.data) | (User.email == form.email.data)) & (User.id != id)
        ).first()

        if existing_user:
            flash('اسم المستخدم أو البريد الإلكتروني موجود بالفعل', 'error')
        else:
            try:
                user.username = form.username.data
                user.email = form.email.data
                user.full_name = form.full_name.data
                user.role = form.role.data
                user.is_active = form.is_active.data

                # Update password if provided
                if form.password.data:
                    user.set_password(form.password.data)

                db.session.commit()

                # Log activity
                log_activity(
                    user_id=current_user.id,
                    action='user_edit',
                    description=f'تعديل المستخدم: {user.username}',
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent')
                )

                flash('تم تحديث المستخدم بنجاح', 'success')
                return redirect(url_for('manage_users'))

            except Exception as e:
                db.session.rollback()
                flash(f'حدث خطأ أثناء تحديث المستخدم: {str(e)}', 'error')

    return render_template('admin_user_form.html', form=form, user=user, title='تعديل المستخدم', action='edit')

@app.route('/admin/user/<int:id>/delete', methods=['POST'])
@login_required
def delete_user(id):
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لحذف المستخدمين', 'error')
        return redirect(url_for('dashboard'))

    user = User.query.get_or_404(id)

    # Prevent admin from deleting themselves
    if user.id == current_user.id:
        flash('لا يمكنك حذف حسابك الخاص', 'error')
        return redirect(url_for('manage_users'))

    try:
        # Instead of deleting, deactivate the user
        user.is_active = False
        db.session.commit()

        # Log activity
        log_activity(
            user_id=current_user.id,
            action='user_deactivate',
            description=f'إلغاء تفعيل المستخدم: {user.username}',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )

        flash('تم إلغاء تفعيل المستخدم بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إلغاء تفعيل المستخدم: {str(e)}', 'error')

    return redirect(url_for('manage_users'))

if __name__ == '__main__':
    from database import init_database
    init_database()
    app.run(debug=True, host='0.0.0.0', port=5000)
