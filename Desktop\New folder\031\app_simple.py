from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_file, abort
from flask_login import Lo<PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from werkzeug.utils import secure_filename
import os
from datetime import datetime, date
from sqlalchemy import or_, and_

# Import our modules
from config import Config
from models import db, User, Document, Category, DocumentType, DocumentComment, ActivityLog, SystemSettings
from forms import (LoginForm, DocumentUploadForm, DocumentEditForm, SearchForm,
                  CommentForm, UserForm, CategoryForm, DocumentTypeForm, ChangePasswordForm)
from utils import (allowed_file, generate_unique_filename, get_file_info, organize_file_path,
                  generate_registration_number, generate_registry_number, log_activity, format_file_size, get_file_icon_class)

# Create Flask app
app = Flask(__name__)
app.config.from_object(Config)

# Initialize extensions
db.init_app(app)

# Initialize Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
login_manager.login_message_category = 'info'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Initialize configuration
Config.init_app(app)

# Routes
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()

        if user and user.check_password(form.password.data) and user.is_active:
            login_user(user, remember=form.remember_me.data)
            user.last_login = datetime.utcnow()
            db.session.commit()

            # Log login activity
            log_activity(
                user_id=user.id,
                action='login',
                description='تسجيل دخول المستخدم',
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent')
            )

            next_page = request.args.get('next')
            if not next_page or not next_page.startswith('/'):
                next_page = url_for('dashboard')
            return redirect(next_page)
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template('login.html', form=form)

@app.route('/logout')
@login_required
def logout():
    log_activity(
        user_id=current_user.id,
        action='logout',
        description='تسجيل خروج المستخدم',
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent')
    )
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    # Get statistics
    total_documents = Document.query.filter_by(status='active').count()
    my_documents = Document.query.filter_by(uploaded_by=current_user.id, status='active').count()
    archived_documents = Document.query.filter_by(is_archived=True, status='active').count()
    confidential_documents = Document.query.filter_by(is_confidential=True, status='active').count()

    # Get recent documents
    recent_documents = Document.query.filter_by(status='active').order_by(Document.created_at.desc()).limit(10).all()

    # Get recent activities
    recent_activities = ActivityLog.query.order_by(ActivityLog.created_at.desc()).limit(10).all()

    stats = {
        'total_documents': total_documents,
        'my_documents': my_documents,
        'archived_documents': archived_documents,
        'confidential_documents': confidential_documents
    }

    return render_template('dashboard.html',
                         stats=stats,
                         recent_documents=recent_documents,
                         recent_activities=recent_activities,
                         get_file_icon_class=get_file_icon_class)

@app.route('/upload', methods=['GET', 'POST'])
@login_required
def upload_document():
    if not current_user.has_permission('upload'):
        flash('ليس لديك صلاحية لرفع الوثائق', 'error')
        return redirect(url_for('dashboard'))

    form = DocumentUploadForm()

    # Populate choices for select fields with hierarchical categories
    categories = Category.query.filter_by(is_active=True).order_by(Category.name_ar).all()
    category_choices = [('', 'اختر التصنيف')]

    # Build hierarchical category list
    def build_category_tree(categories, parent_id=None, level=0):
        choices = []
        for category in categories:
            if category.parent_id == parent_id:
                prefix = "└─ " * level if level > 0 else ""
                choices.append((category.id, f"{prefix}{category.name_ar}"))
                # Add subcategories
                choices.extend(build_category_tree(categories, category.id, level + 1))
        return choices

    category_choices.extend(build_category_tree(categories))
    form.category_id.choices = category_choices

    form.document_type_id.choices = [('', 'اختر نوع الوثيقة')] + [(dt.id, dt.name_ar) for dt in DocumentType.query.order_by(DocumentType.name_ar).all()]

    if form.validate_on_submit():
        file = form.file.data

        if file and allowed_file(file.filename):
            try:
                # Handle category selection/creation
                if form.category_selection.data == 'new':
                    # Create new category
                    if not form.new_category_ar.data or not form.new_category_en.data:
                        flash('يرجى إدخال اسم التصنيف باللغتين العربية والإنجليزية', 'error')
                        return render_template('upload.html', form=form)

                    # Check if category already exists
                    existing_category = Category.query.filter(
                        (Category.name_ar == form.new_category_ar.data) |
                        (Category.name_en == form.new_category_en.data)
                    ).first()

                    if existing_category:
                        category = existing_category
                        flash(f'التصنيف "{existing_category.name_ar}" موجود بالفعل، تم استخدامه', 'info')
                    else:
                        category = Category(
                            name_ar=form.new_category_ar.data,
                            name_en=form.new_category_en.data,
                            description=f'تصنيف تم إنشاؤه تلقائياً عند رفع الوثيقة'
                        )
                        db.session.add(category)
                        db.session.flush()  # Get the ID
                        flash(f'تم إنشاء تصنيف جديد: {category.name_ar}', 'success')
                else:
                    # Use existing category
                    if not form.category_id.data:
                        flash('يرجى اختيار التصنيف', 'error')
                        return render_template('upload.html', form=form)
                    category = Category.query.get(form.category_id.data)

                # Handle document type selection/creation
                if form.document_type_selection.data == 'new':
                    # Create new document type
                    if not form.new_document_type_ar.data or not form.new_document_type_en.data:
                        flash('يرجى إدخال اسم نوع الوثيقة باللغتين العربية والإنجليزية', 'error')
                        return render_template('upload.html', form=form)

                    # Check if document type already exists
                    existing_doc_type = DocumentType.query.filter(
                        (DocumentType.name_ar == form.new_document_type_ar.data) |
                        (DocumentType.name_en == form.new_document_type_en.data)
                    ).first()

                    if existing_doc_type:
                        document_type = existing_doc_type
                        flash(f'نوع الوثيقة "{existing_doc_type.name_ar}" موجود بالفعل، تم استخدامه', 'info')
                    else:
                        document_type = DocumentType(
                            name_ar=form.new_document_type_ar.data,
                            name_en=form.new_document_type_en.data,
                            priority=form.new_document_type_priority.data,
                            is_confidential=form.is_confidential.data
                        )
                        db.session.add(document_type)
                        db.session.flush()  # Get the ID
                        flash(f'تم إنشاء نوع وثيقة جديد: {document_type.name_ar}', 'success')
                else:
                    # Use existing document type
                    if not form.document_type_id.data:
                        flash('يرجى اختيار نوع الوثيقة', 'error')
                        return render_template('upload.html', form=form)
                    document_type = DocumentType.query.get(form.document_type_id.data)

                # Generate unique filename
                filename = generate_unique_filename(file.filename)

                # Get category for file organization
                file_path = organize_file_path(filename, category.name_en)
                full_path = os.path.join(app.config['UPLOAD_FOLDER'], 'documents', file_path)

                # Save file
                file.save(full_path)

                # Get file information
                file_info = get_file_info(full_path)

                # Generate registration number based on registry type
                registry_prefix = {
                    'incoming': 'IN',
                    'outgoing': 'OUT',
                    'general': 'GEN'
                }.get(form.registry_type.data, 'GEN')

                reg_number = generate_registration_number(registry_prefix)

                # Generate registry-specific number
                registry_number = generate_registry_number(form.registry_type.data)

                # Create document record
                document = Document(
                    registration_number=reg_number,
                    title=form.title.data,
                    description=form.description.data,
                    sender=form.sender.data,
                    receiver=form.receiver.data,
                    subject=form.subject.data,
                    filename=filename,
                    original_filename=file.filename,
                    file_path=file_path,
                    file_size=file_info['size'],
                    file_type=file.filename.split('.')[-1].lower(),
                    mime_type=file_info['mime_type'],
                    document_date=form.document_date.data,
                    received_date=form.received_date.data,
                    category_id=category.id,
                    document_type_id=document_type.id,
                    uploaded_by=current_user.id,
                    is_confidential=form.is_confidential.data,
                    direction=form.direction.data,
                    registry_type=form.registry_type.data,
                    registry_number=registry_number
                )

                db.session.add(document)
                db.session.commit()

                # Add tags if provided
                if form.tags.data:
                    from models import DocumentTag
                    tags = [tag.strip() for tag in form.tags.data.split(',') if tag.strip()]
                    for tag_name in tags:
                        tag = DocumentTag(document_id=document.id, tag_name=tag_name)
                        db.session.add(tag)

                db.session.commit()

                # Log activity
                log_activity(
                    user_id=current_user.id,
                    action='upload',
                    document_id=document.id,
                    description=f'رفع وثيقة جديدة: {document.title}',
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent')
                )

                flash(f'تم رفع الوثيقة بنجاح. رقم التسجيل: {reg_number}', 'success')
                return redirect(url_for('view_document', id=document.id))

            except Exception as e:
                db.session.rollback()
                flash(f'حدث خطأ أثناء رفع الوثيقة: {str(e)}', 'error')
        else:
            flash('نوع الملف غير مدعوم', 'error')

    return render_template('upload.html', form=form)

@app.route('/documents')
@login_required
def all_documents():
    """عرض جميع الوثائق مع إمكانية الفلترة والطباعة"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)

    # Build base query
    query = Document.query.filter_by(status='active')

    # Apply filters
    category_id = request.args.get('category_id')
    document_type_id = request.args.get('document_type_id')
    registry_type = request.args.get('registry_type')
    is_confidential = request.args.get('is_confidential')
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    search_query = request.args.get('search')

    # Category filter
    if category_id and category_id != '':
        try:
            query = query.filter_by(category_id=int(category_id))
        except (ValueError, TypeError):
            pass

    # Document type filter
    if document_type_id and document_type_id != '':
        try:
            query = query.filter_by(document_type_id=int(document_type_id))
        except (ValueError, TypeError):
            pass

    # Registry type filter
    if registry_type and registry_type != '':
        query = query.filter_by(registry_type=registry_type)

    # Confidential filter
    if is_confidential and is_confidential != '':
        confidential_bool = is_confidential.lower() == 'true'
        query = query.filter_by(is_confidential=confidential_bool)

    # Date range filter
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            query = query.filter(Document.document_date >= date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            query = query.filter(Document.document_date <= date_to_obj)
        except ValueError:
            pass

    # Search filter
    if search_query:
        search_term = f"%{search_query}%"
        query = query.filter(
            or_(
                Document.title.like(search_term),
                Document.description.like(search_term),
                Document.subject.like(search_term),
                Document.sender.like(search_term),
                Document.receiver.like(search_term),
                Document.registration_number.like(search_term),
                Document.registry_number.like(search_term)
            )
        )

    # Check permissions for confidential documents
    if current_user.role == 'viewer':
        query = query.filter_by(is_confidential=False)

    # Get sorting parameters
    sort_by = request.args.get('sort_by', 'created_at')
    sort_order = request.args.get('sort_order', 'desc')

    # Apply sorting
    if sort_by == 'title':
        if sort_order == 'asc':
            query = query.order_by(Document.title.asc())
        else:
            query = query.order_by(Document.title.desc())
    elif sort_by == 'document_date':
        if sort_order == 'asc':
            query = query.order_by(Document.document_date.asc())
        else:
            query = query.order_by(Document.document_date.desc())
    elif sort_by == 'registry_number':
        if sort_order == 'asc':
            query = query.order_by(Document.registry_number.asc())
        else:
            query = query.order_by(Document.registry_number.desc())
    else:  # created_at
        if sort_order == 'asc':
            query = query.order_by(Document.created_at.asc())
        else:
            query = query.order_by(Document.created_at.desc())

    # Paginate results
    documents = query.paginate(
        page=page, per_page=per_page, error_out=False
    )

    # Get filter options
    categories = Category.query.filter_by(is_active=True).order_by(Category.name_ar).all()
    document_types = DocumentType.query.order_by(DocumentType.name_ar).all()

    # Get statistics
    total_documents = Document.query.filter_by(status='active').count()
    filtered_count = query.count() if any([category_id, document_type_id, registry_type, is_confidential, date_from, date_to, search_query]) else total_documents

    return render_template('all_documents.html',
                         documents=documents,
                         categories=categories,
                         document_types=document_types,
                         total_documents=total_documents,
                         filtered_count=filtered_count,
                         get_file_icon_class=get_file_icon_class,
                         format_file_size=format_file_size,
                         # Current filter values
                         current_category=category_id,
                         current_document_type=document_type_id,
                         current_registry_type=registry_type,
                         current_confidential=is_confidential,
                         current_date_from=date_from,
                         current_date_to=date_to,
                         current_search=search_query,
                         current_sort_by=sort_by,
                         current_sort_order=sort_order,
                         current_per_page=per_page)

@app.route('/documents/print')
@login_required
def print_documents():
    """طباعة قائمة الوثائق"""
    # Get same filters as all_documents
    query = Document.query.filter_by(status='active')

    # Apply all filters (same logic as all_documents)
    category_id = request.args.get('category_id')
    document_type_id = request.args.get('document_type_id')
    registry_type = request.args.get('registry_type')
    is_confidential = request.args.get('is_confidential')
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    search_query = request.args.get('search')

    # Apply filters (same as above)
    if category_id and category_id != '':
        try:
            query = query.filter_by(category_id=int(category_id))
        except (ValueError, TypeError):
            pass

    if document_type_id and document_type_id != '':
        try:
            query = query.filter_by(document_type_id=int(document_type_id))
        except (ValueError, TypeError):
            pass

    if registry_type and registry_type != '':
        query = query.filter_by(registry_type=registry_type)

    if is_confidential and is_confidential != '':
        confidential_bool = is_confidential.lower() == 'true'
        query = query.filter_by(is_confidential=confidential_bool)

    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            query = query.filter(Document.document_date >= date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            query = query.filter(Document.document_date <= date_to_obj)
        except ValueError:
            pass

    if search_query:
        search_term = f"%{search_query}%"
        query = query.filter(
            or_(
                Document.title.like(search_term),
                Document.description.like(search_term),
                Document.subject.like(search_term),
                Document.sender.like(search_term),
                Document.receiver.like(search_term),
                Document.registration_number.like(search_term),
                Document.registry_number.like(search_term)
            )
        )

    # Check permissions for confidential documents
    if current_user.role == 'viewer':
        query = query.filter_by(is_confidential=False)

    # Get sorting
    sort_by = request.args.get('sort_by', 'created_at')
    sort_order = request.args.get('sort_order', 'desc')

    if sort_by == 'title':
        if sort_order == 'asc':
            query = query.order_by(Document.title.asc())
        else:
            query = query.order_by(Document.title.desc())
    elif sort_by == 'document_date':
        if sort_order == 'asc':
            query = query.order_by(Document.document_date.asc())
        else:
            query = query.order_by(Document.document_date.desc())
    elif sort_by == 'registry_number':
        if sort_order == 'asc':
            query = query.order_by(Document.registry_number.asc())
        else:
            query = query.order_by(Document.registry_number.desc())
    else:  # created_at
        if sort_order == 'asc':
            query = query.order_by(Document.created_at.asc())
        else:
            query = query.order_by(Document.created_at.desc())

    # Get all documents (no pagination for print)
    documents = query.all()

    # Get filter info for print header
    filter_info = {}
    if category_id:
        category = Category.query.get(category_id)
        if category:
            filter_info['category'] = category.name_ar

    if document_type_id:
        doc_type = DocumentType.query.get(document_type_id)
        if doc_type:
            filter_info['document_type'] = doc_type.name_ar

    if registry_type:
        registry_names = {
            'incoming': 'السجل الوارد',
            'outgoing': 'السجل الصادر',
            'general': 'الأرشيف العام'
        }
        filter_info['registry_type'] = registry_names.get(registry_type, registry_type)

    if is_confidential:
        filter_info['confidential'] = 'سرية' if is_confidential.lower() == 'true' else 'غير سرية'

    if date_from:
        filter_info['date_from'] = date_from

    if date_to:
        filter_info['date_to'] = date_to

    if search_query:
        filter_info['search'] = search_query

    return render_template('print_documents.html',
                         documents=documents,
                         filter_info=filter_info,
                         total_count=len(documents),
                         print_date=datetime.now(),
                         current_user=current_user)

@app.route('/search')
@login_required
def search_documents():
    form = SearchForm()

    # Populate choices
    form.category_id.choices = [('', 'جميع التصنيفات')] + [(c.id, c.name_ar) for c in Category.query.filter_by(is_active=True).all()]
    form.document_type_id.choices = [('', 'جميع الأنواع')] + [(dt.id, dt.name_ar) for dt in DocumentType.query.all()]

    documents = []
    page = request.args.get('page', 1, type=int)

    if request.args.get('query') or any(request.args.get(field) for field in ['category_id', 'document_type_id', 'sender', 'receiver', 'date_from', 'date_to']):
        # Build query
        query = Document.query.filter_by(status='active')

        # Text search
        if request.args.get('query'):
            search_term = f"%{request.args.get('query')}%"
            query = query.filter(
                or_(
                    Document.title.like(search_term),
                    Document.description.like(search_term),
                    Document.subject.like(search_term),
                    Document.sender.like(search_term),
                    Document.receiver.like(search_term),
                    Document.registration_number.like(search_term)
                )
            )

        # Category filter
        if request.args.get('category_id') and request.args.get('category_id') != '':
            try:
                category_id = int(request.args.get('category_id'))
                query = query.filter_by(category_id=category_id)
            except (ValueError, TypeError):
                pass

        # Document type filter
        if request.args.get('document_type_id') and request.args.get('document_type_id') != '':
            try:
                document_type_id = int(request.args.get('document_type_id'))
                query = query.filter_by(document_type_id=document_type_id)
            except (ValueError, TypeError):
                pass

        # Check permissions for confidential documents
        if current_user.role == 'viewer':
            query = query.filter_by(is_confidential=False)

        # Paginate results
        documents = query.order_by(Document.created_at.desc()).paginate(
            page=page, per_page=app.config['DOCUMENTS_PER_PAGE'], error_out=False
        )

    return render_template('search.html', form=form, documents=documents, get_file_icon_class=get_file_icon_class)

@app.route('/document/<int:id>')
@login_required
def view_document(id):
    document = Document.query.get_or_404(id)

    # Check permissions
    if document.is_confidential and current_user.role == 'viewer':
        flash('ليس لديك صلاحية لعرض هذه الوثيقة السرية', 'error')
        return redirect(url_for('dashboard'))

    # Log view activity
    log_activity(
        user_id=current_user.id,
        action='view',
        document_id=document.id,
        description=f'عرض الوثيقة: {document.title}',
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent')
    )

    return render_template('document_view.html',
                         document=document,
                         format_file_size=format_file_size,
                         get_file_icon_class=get_file_icon_class)

@app.route('/document/<int:id>/download')
@login_required
def download_document(id):
    document = Document.query.get_or_404(id)

    # Check permissions
    if document.is_confidential and current_user.role == 'viewer':
        flash('ليس لديك صلاحية لتحميل هذه الوثيقة السرية', 'error')
        return redirect(url_for('view_document', id=id))

    file_path = os.path.join(app.config['UPLOAD_FOLDER'], 'documents', document.file_path)

    if not os.path.exists(file_path):
        flash('الملف غير موجود', 'error')
        return redirect(url_for('view_document', id=id))

    # Log download activity
    log_activity(
        user_id=current_user.id,
        action='download',
        document_id=document.id,
        description=f'تحميل الوثيقة: {document.title}',
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent')
    )

    return send_file(file_path, as_attachment=True, download_name=document.original_filename)

@app.route('/document/<int:id>/view_file')
@login_required
def view_file(id):
    """عرض الملف مباشرة في المتصفح"""
    document = Document.query.get_or_404(id)

    # Check permissions
    if document.is_confidential and current_user.role == 'viewer':
        flash('ليس لديك صلاحية لعرض هذه الوثيقة السرية', 'error')
        return redirect(url_for('dashboard'))

    # Get file path
    file_path = os.path.join(app.config['UPLOAD_FOLDER'], 'documents', document.file_path)

    if not os.path.exists(file_path):
        flash('الملف غير موجود', 'error')
        return redirect(url_for('view_document', id=id))

    # Get file extension
    file_ext = os.path.splitext(document.filename)[1].lower()

    # Determine if file can be viewed in browser
    viewable_extensions = ['.pdf', '.txt', '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg']

    if file_ext in viewable_extensions:
        # Log activity
        log_activity(
            user_id=current_user.id,
            action='view_file',
            document_id=document.id,
            description=f'عرض الملف: {document.title}',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )

        # Return file for viewing
        return send_file(file_path, as_attachment=False)
    else:
        flash('نوع الملف غير مدعوم للعرض المباشر', 'warning')
        return redirect(url_for('view_document', id=id))

@app.route('/document/<int:id>/print_file')
@login_required
def print_file(id):
    """طباعة الملف"""
    document = Document.query.get_or_404(id)

    # Check permissions
    if document.is_confidential and current_user.role == 'viewer':
        flash('ليس لديك صلاحية لطباعة هذه الوثيقة السرية', 'error')
        return redirect(url_for('dashboard'))

    # Get file path
    file_path = os.path.join(app.config['UPLOAD_FOLDER'], 'documents', document.file_path)

    if not os.path.exists(file_path):
        flash('الملف غير موجود', 'error')
        return redirect(url_for('view_document', id=id))

    # Get file extension
    file_ext = os.path.splitext(document.filename)[1].lower()

    # Check if file type supports printing
    printable_extensions = ['.pdf', '.txt', '.jpg', '.jpeg', '.png', '.gif', '.bmp']

    if file_ext in printable_extensions:
        # Log activity
        log_activity(
            user_id=current_user.id,
            action='print_file',
            document_id=document.id,
            description=f'طباعة الملف: {document.title}',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )

        # For images and text files, create a print-friendly page
        if file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
            return render_template('print_image.html',
                                 document=document,
                                 file_url=url_for('view_file', id=id),
                                 current_user=current_user)
        elif file_ext == '.txt':
            # Read text file content
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            except UnicodeDecodeError:
                try:
                    with open(file_path, 'r', encoding='windows-1256') as f:
                        content = f.read()
                except:
                    content = "لا يمكن قراءة محتوى الملف"

            return render_template('print_text.html',
                                 document=document,
                                 content=content,
                                 current_user=current_user)
        else:  # PDF
            # For PDF, redirect to view with print parameter
            return redirect(url_for('view_file', id=id) + '?print=true')
    else:
        flash('نوع الملف غير مدعوم للطباعة المباشرة', 'warning')
        return redirect(url_for('view_document', id=id))

@app.route('/document/<int:id>/file_viewer')
@login_required
def file_viewer(id):
    """صفحة عارض الملفات المتقدم"""
    document = Document.query.get_or_404(id)

    # Check permissions
    if document.is_confidential and current_user.role == 'viewer':
        flash('ليس لديك صلاحية لعرض هذه الوثيقة السرية', 'error')
        return redirect(url_for('dashboard'))

    # Get file path
    file_path = os.path.join(app.config['UPLOAD_FOLDER'], 'documents', document.file_path)

    if not os.path.exists(file_path):
        flash('الملف غير موجود', 'error')
        return redirect(url_for('view_document', id=id))

    # Get file extension and info
    file_ext = os.path.splitext(document.filename)[1].lower()
    file_info = get_file_info(file_path)

    # Determine viewer type
    viewer_type = 'unsupported'
    if file_ext == '.pdf':
        viewer_type = 'pdf'
    elif file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg']:
        viewer_type = 'image'
    elif file_ext == '.txt':
        viewer_type = 'text'
    elif file_ext in ['.doc', '.docx']:
        viewer_type = 'document'
    elif file_ext in ['.xls', '.xlsx']:
        viewer_type = 'spreadsheet'
    elif file_ext in ['.ppt', '.pptx']:
        viewer_type = 'presentation'

    # For text files, read content
    text_content = None
    if viewer_type == 'text':
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                text_content = f.read()
        except UnicodeDecodeError:
            try:
                with open(file_path, 'r', encoding='windows-1256') as f:
                    text_content = f.read()
            except:
                text_content = "لا يمكن قراءة محتوى الملف"

    # Log activity
    log_activity(
        user_id=current_user.id,
        action='view_file_viewer',
        document_id=document.id,
        description=f'عرض الملف في العارض: {document.title}',
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent')
    )

    return render_template('file_viewer.html',
                         document=document,
                         file_info=file_info,
                         viewer_type=viewer_type,
                         file_ext=file_ext,
                         text_content=text_content,
                         current_user=current_user,
                         format_file_size=format_file_size)

# Profile and User Management Routes
@app.route('/profile')
@login_required
def profile():
    return render_template('profile.html', user=current_user)

@app.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    form = ChangePasswordForm()

    if form.validate_on_submit():
        if current_user.check_password(form.current_password.data):
            current_user.set_password(form.new_password.data)
            db.session.commit()

            # Log activity
            log_activity(
                user_id=current_user.id,
                action='password_change',
                description='تغيير كلمة المرور',
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent')
            )

            flash('تم تغيير كلمة المرور بنجاح', 'success')
            return redirect(url_for('profile'))
        else:
            flash('كلمة المرور الحالية غير صحيحة', 'error')

    return render_template('change_password.html', form=form)

# Admin Routes
@app.route('/admin')
@login_required
def admin_panel():
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى لوحة الإدارة', 'error')
        return redirect(url_for('dashboard'))

    # Get statistics
    total_users = User.query.count()
    total_documents = Document.query.filter_by(status='active').count()
    total_categories = Category.query.filter_by(is_active=True).count()
    recent_activities = ActivityLog.query.order_by(ActivityLog.created_at.desc()).limit(20).all()

    stats = {
        'total_users': total_users,
        'total_documents': total_documents,
        'total_categories': total_categories
    }

    return render_template('admin.html', stats=stats, recent_activities=recent_activities)

@app.route('/admin/users')
@login_required
def manage_users():
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لإدارة المستخدمين', 'error')
        return redirect(url_for('dashboard'))

    page = request.args.get('page', 1, type=int)
    users = User.query.paginate(page=page, per_page=20, error_out=False)

    return render_template('admin_users.html', users=users)

@app.route('/admin/user/add', methods=['GET', 'POST'])
@login_required
def add_user():
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لإضافة مستخدمين', 'error')
        return redirect(url_for('dashboard'))

    form = UserForm()

    if form.validate_on_submit():
        # Check if username or email already exists
        existing_user = User.query.filter(
            (User.username == form.username.data) | (User.email == form.email.data)
        ).first()

        if existing_user:
            flash('اسم المستخدم أو البريد الإلكتروني موجود بالفعل', 'error')
        else:
            try:
                user = User(
                    username=form.username.data,
                    email=form.email.data,
                    full_name=form.full_name.data,
                    role=form.role.data,
                    is_active=form.is_active.data
                )
                user.set_password(form.password.data)

                db.session.add(user)
                db.session.commit()

                # Log activity
                log_activity(
                    user_id=current_user.id,
                    action='user_add',
                    description=f'إضافة مستخدم جديد: {user.username}',
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent')
                )

                flash('تم إضافة المستخدم بنجاح', 'success')
                return redirect(url_for('manage_users'))

            except Exception as e:
                db.session.rollback()
                flash(f'حدث خطأ أثناء إضافة المستخدم: {str(e)}', 'error')

    return render_template('admin_user_form.html', form=form, title='إضافة مستخدم جديد', action='add')

@app.route('/admin/user/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_user(id):
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لتعديل المستخدمين', 'error')
        return redirect(url_for('dashboard'))

    user = User.query.get_or_404(id)
    form = UserForm(obj=user)

    if form.validate_on_submit():
        # Check if username or email already exists (excluding current user)
        existing_user = User.query.filter(
            ((User.username == form.username.data) | (User.email == form.email.data)) & (User.id != id)
        ).first()

        if existing_user:
            flash('اسم المستخدم أو البريد الإلكتروني موجود بالفعل', 'error')
        else:
            try:
                user.username = form.username.data
                user.email = form.email.data
                user.full_name = form.full_name.data
                user.role = form.role.data
                user.is_active = form.is_active.data

                # Update password if provided
                if form.password.data:
                    user.set_password(form.password.data)

                db.session.commit()

                # Log activity
                log_activity(
                    user_id=current_user.id,
                    action='user_edit',
                    description=f'تعديل المستخدم: {user.username}',
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent')
                )

                flash('تم تحديث المستخدم بنجاح', 'success')
                return redirect(url_for('manage_users'))

            except Exception as e:
                db.session.rollback()
                flash(f'حدث خطأ أثناء تحديث المستخدم: {str(e)}', 'error')

    return render_template('admin_user_form.html', form=form, user=user, title='تعديل المستخدم', action='edit')

@app.route('/admin/user/<int:id>/delete', methods=['POST'])
@login_required
def delete_user(id):
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لحذف المستخدمين', 'error')
        return redirect(url_for('dashboard'))

    user = User.query.get_or_404(id)

    # Prevent admin from deleting themselves
    if user.id == current_user.id:
        flash('لا يمكنك حذف حسابك الخاص', 'error')
        return redirect(url_for('manage_users'))

    try:
        # Instead of deleting, deactivate the user
        user.is_active = False
        db.session.commit()

        # Log activity
        log_activity(
            user_id=current_user.id,
            action='user_deactivate',
            description=f'إلغاء تفعيل المستخدم: {user.username}',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )

        flash('تم إلغاء تفعيل المستخدم بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إلغاء تفعيل المستخدم: {str(e)}', 'error')

    return redirect(url_for('manage_users'))

# Categories Management Routes
@app.route('/admin/categories')
@login_required
def manage_categories():
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لإدارة التصنيفات', 'error')
        return redirect(url_for('dashboard'))

    page = request.args.get('page', 1, type=int)
    categories = Category.query.paginate(page=page, per_page=20, error_out=False)

    return render_template('admin_categories.html', categories=categories)

@app.route('/admin/category/add', methods=['GET', 'POST'])
@login_required
def add_category():
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لإضافة التصنيفات', 'error')
        return redirect(url_for('dashboard'))

    form = CategoryForm()

    # Populate parent categories
    form.parent_id.choices = [('', 'بدون تصنيف أب')] + [(c.id, c.name_ar) for c in Category.query.filter_by(is_active=True).all()]

    if form.validate_on_submit():
        # Check if category already exists
        existing_category = Category.query.filter(
            (Category.name_ar == form.name_ar.data) | (Category.name_en == form.name_en.data)
        ).first()

        if existing_category:
            flash('التصنيف موجود بالفعل', 'error')
        else:
            try:
                category = Category(
                    name_ar=form.name_ar.data,
                    name_en=form.name_en.data,
                    description=form.description.data,
                    parent_id=form.parent_id.data if form.parent_id.data else None,
                    is_active=form.is_active.data
                )

                db.session.add(category)
                db.session.commit()

                # Log activity
                log_activity(
                    user_id=current_user.id,
                    action='category_add',
                    description=f'إضافة تصنيف جديد: {category.name_ar}',
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent')
                )

                flash('تم إضافة التصنيف بنجاح', 'success')
                return redirect(url_for('manage_categories'))

            except Exception as e:
                db.session.rollback()
                flash(f'حدث خطأ أثناء إضافة التصنيف: {str(e)}', 'error')

    return render_template('admin_category_form.html', form=form, title='إضافة تصنيف جديد', action='add')

@app.route('/admin/category/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_category(id):
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لتعديل التصنيفات', 'error')
        return redirect(url_for('dashboard'))

    category = Category.query.get_or_404(id)
    form = CategoryForm(obj=category)

    # Populate parent categories (excluding current category and its children)
    available_parents = Category.query.filter(
        Category.id != id,
        Category.is_active == True
    ).all()
    form.parent_id.choices = [('', 'بدون تصنيف أب')] + [(c.id, c.name_ar) for c in available_parents]

    if form.validate_on_submit():
        # Check if category name already exists (excluding current category)
        existing_category = Category.query.filter(
            ((Category.name_ar == form.name_ar.data) | (Category.name_en == form.name_en.data)) & (Category.id != id)
        ).first()

        if existing_category:
            flash('اسم التصنيف موجود بالفعل', 'error')
        else:
            try:
                category.name_ar = form.name_ar.data
                category.name_en = form.name_en.data
                category.description = form.description.data
                category.parent_id = form.parent_id.data if form.parent_id.data else None
                category.is_active = form.is_active.data

                db.session.commit()

                # Log activity
                log_activity(
                    user_id=current_user.id,
                    action='category_edit',
                    description=f'تعديل التصنيف: {category.name_ar}',
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent')
                )

                flash('تم تحديث التصنيف بنجاح', 'success')
                return redirect(url_for('manage_categories'))

            except Exception as e:
                db.session.rollback()
                flash(f'حدث خطأ أثناء تحديث التصنيف: {str(e)}', 'error')

    return render_template('admin_category_form.html', form=form, category=category, title='تعديل التصنيف', action='edit')

@app.route('/admin/category/<int:id>/delete', methods=['POST'])
@login_required
def delete_category(id):
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لحذف التصنيفات', 'error')
        return redirect(url_for('dashboard'))

    category = Category.query.get_or_404(id)

    # Check if category has documents
    document_count = Document.query.filter_by(category_id=id, status='active').count()
    if document_count > 0:
        flash(f'لا يمكن حذف التصنيف لأنه يحتوي على {document_count} وثيقة', 'error')
        return redirect(url_for('manage_categories'))

    # Check if category has subcategories
    subcategory_count = Category.query.filter_by(parent_id=id).count()
    if subcategory_count > 0:
        flash(f'لا يمكن حذف التصنيف لأنه يحتوي على {subcategory_count} تصنيف فرعي', 'error')
        return redirect(url_for('manage_categories'))

    try:
        # Instead of deleting, deactivate the category
        category.is_active = False
        db.session.commit()

        # Log activity
        log_activity(
            user_id=current_user.id,
            action='category_deactivate',
            description=f'إلغاء تفعيل التصنيف: {category.name_ar}',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )

        flash('تم إلغاء تفعيل التصنيف بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إلغاء تفعيل التصنيف: {str(e)}', 'error')

    return redirect(url_for('manage_categories'))

# Document Types Management Routes
@app.route('/admin/document-types')
@login_required
def manage_document_types():
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لإدارة أنواع الوثائق', 'error')
        return redirect(url_for('dashboard'))

    page = request.args.get('page', 1, type=int)
    document_types = DocumentType.query.paginate(page=page, per_page=20, error_out=False)

    return render_template('admin_document_types.html', document_types=document_types)

@app.route('/admin/document-type/add', methods=['GET', 'POST'])
@login_required
def add_document_type():
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لإضافة أنواع الوثائق', 'error')
        return redirect(url_for('dashboard'))

    form = DocumentTypeForm()

    if form.validate_on_submit():
        # Check if document type already exists
        existing_type = DocumentType.query.filter(
            (DocumentType.name_ar == form.name_ar.data) | (DocumentType.name_en == form.name_en.data)
        ).first()

        if existing_type:
            flash('نوع الوثيقة موجود بالفعل', 'error')
        else:
            try:
                document_type = DocumentType(
                    name_ar=form.name_ar.data,
                    name_en=form.name_en.data,
                    priority=form.priority.data,
                    is_confidential=form.is_confidential.data
                )

                db.session.add(document_type)
                db.session.commit()

                # Log activity
                log_activity(
                    user_id=current_user.id,
                    action='document_type_add',
                    description=f'إضافة نوع وثيقة جديد: {document_type.name_ar}',
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent')
                )

                flash('تم إضافة نوع الوثيقة بنجاح', 'success')
                return redirect(url_for('manage_document_types'))

            except Exception as e:
                db.session.rollback()
                flash(f'حدث خطأ أثناء إضافة نوع الوثيقة: {str(e)}', 'error')

    return render_template('admin_document_type_form.html', form=form, title='إضافة نوع وثيقة جديد', action='add')

@app.route('/admin/document-type/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_document_type(id):
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لتعديل أنواع الوثائق', 'error')
        return redirect(url_for('dashboard'))

    document_type = DocumentType.query.get_or_404(id)
    form = DocumentTypeForm(obj=document_type)

    if form.validate_on_submit():
        # Check if document type name already exists (excluding current type)
        existing_type = DocumentType.query.filter(
            ((DocumentType.name_ar == form.name_ar.data) | (DocumentType.name_en == form.name_en.data)) & (DocumentType.id != id)
        ).first()

        if existing_type:
            flash('اسم نوع الوثيقة موجود بالفعل', 'error')
        else:
            try:
                document_type.name_ar = form.name_ar.data
                document_type.name_en = form.name_en.data
                document_type.priority = form.priority.data
                document_type.is_confidential = form.is_confidential.data

                db.session.commit()

                # Log activity
                log_activity(
                    user_id=current_user.id,
                    action='document_type_edit',
                    description=f'تعديل نوع الوثيقة: {document_type.name_ar}',
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent')
                )

                flash('تم تحديث نوع الوثيقة بنجاح', 'success')
                return redirect(url_for('manage_document_types'))

            except Exception as e:
                db.session.rollback()
                flash(f'حدث خطأ أثناء تحديث نوع الوثيقة: {str(e)}', 'error')

    return render_template('admin_document_type_form.html', form=form, document_type=document_type, title='تعديل نوع الوثيقة', action='edit')

@app.route('/admin/document-type/<int:id>/delete', methods=['POST'])
@login_required
def delete_document_type(id):
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لحذف أنواع الوثائق', 'error')
        return redirect(url_for('dashboard'))

    document_type = DocumentType.query.get_or_404(id)

    # Check if document type has documents
    document_count = Document.query.filter_by(document_type_id=id, status='active').count()
    if document_count > 0:
        flash(f'لا يمكن حذف نوع الوثيقة لأنه مستخدم في {document_count} وثيقة', 'error')
        return redirect(url_for('manage_document_types'))

    try:
        db.session.delete(document_type)
        db.session.commit()

        # Log activity
        log_activity(
            user_id=current_user.id,
            action='document_type_delete',
            description=f'حذف نوع الوثيقة: {document_type.name_ar}',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )

        flash('تم حذف نوع الوثيقة بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف نوع الوثيقة: {str(e)}', 'error')

    return redirect(url_for('manage_document_types'))

# API Routes for dynamic updates
@app.route('/api/categories')
@login_required
def api_categories():
    """Get categories list for dynamic updates"""
    categories = Category.query.filter_by(is_active=True).order_by(Category.name_ar).all()

    def build_category_tree(categories, parent_id=None, level=0):
        choices = []
        for category in categories:
            if category.parent_id == parent_id:
                prefix = "└─ " * level if level > 0 else ""
                choices.append({
                    'id': category.id,
                    'name': f"{prefix}{category.name_ar}",
                    'name_ar': category.name_ar,
                    'name_en': category.name_en,
                    'level': level
                })
                # Add subcategories
                choices.extend(build_category_tree(categories, category.id, level + 1))
        return choices

    category_list = build_category_tree(categories)
    return {'categories': category_list}

@app.route('/api/document-types')
@login_required
def api_document_types():
    """Get document types list for dynamic updates"""
    document_types = DocumentType.query.order_by(DocumentType.name_ar).all()

    types_list = []
    for dt in document_types:
        types_list.append({
            'id': dt.id,
            'name_ar': dt.name_ar,
            'name_en': dt.name_en,
            'priority': dt.priority,
            'is_confidential': dt.is_confidential
        })

    return {'document_types': types_list}

@app.route('/api/create-category', methods=['POST'])
@login_required
def api_create_category():
    """Create new category via API"""
    if current_user.role not in ['admin', 'employee']:
        return {'success': False, 'message': 'ليس لديك صلاحية لإنشاء التصنيفات'}, 403

    data = request.get_json()

    if not data or not data.get('name_ar') or not data.get('name_en'):
        return {'success': False, 'message': 'يرجى إدخال اسم التصنيف باللغتين'}, 400

    # Check if category already exists
    existing_category = Category.query.filter(
        (Category.name_ar == data['name_ar']) | (Category.name_en == data['name_en'])
    ).first()

    if existing_category:
        return {
            'success': True,
            'category': {
                'id': existing_category.id,
                'name_ar': existing_category.name_ar,
                'name_en': existing_category.name_en
            },
            'message': f'التصنيف "{existing_category.name_ar}" موجود بالفعل'
        }

    try:
        category = Category(
            name_ar=data['name_ar'],
            name_en=data['name_en'],
            description=data.get('description', f'تصنيف تم إنشاؤه تلقائياً'),
            parent_id=data.get('parent_id') if data.get('parent_id') else None
        )

        db.session.add(category)
        db.session.commit()

        # Log activity
        log_activity(
            user_id=current_user.id,
            action='category_add',
            description=f'إضافة تصنيف جديد: {category.name_ar}',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )

        return {
            'success': True,
            'category': {
                'id': category.id,
                'name_ar': category.name_ar,
                'name_en': category.name_en
            },
            'message': f'تم إنشاء التصنيف "{category.name_ar}" بنجاح'
        }

    except Exception as e:
        db.session.rollback()
        return {'success': False, 'message': f'حدث خطأ: {str(e)}'}, 500

@app.route('/api/create-document-type', methods=['POST'])
@login_required
def api_create_document_type():
    """Create new document type via API"""
    if current_user.role not in ['admin', 'employee']:
        return {'success': False, 'message': 'ليس لديك صلاحية لإنشاء أنواع الوثائق'}, 403

    data = request.get_json()

    if not data or not data.get('name_ar') or not data.get('name_en'):
        return {'success': False, 'message': 'يرجى إدخال اسم نوع الوثيقة باللغتين'}, 400

    # Check if document type already exists
    existing_type = DocumentType.query.filter(
        (DocumentType.name_ar == data['name_ar']) | (DocumentType.name_en == data['name_en'])
    ).first()

    if existing_type:
        return {
            'success': True,
            'document_type': {
                'id': existing_type.id,
                'name_ar': existing_type.name_ar,
                'name_en': existing_type.name_en,
                'priority': existing_type.priority
            },
            'message': f'نوع الوثيقة "{existing_type.name_ar}" موجود بالفعل'
        }

    try:
        document_type = DocumentType(
            name_ar=data['name_ar'],
            name_en=data['name_en'],
            priority=data.get('priority', 'normal'),
            is_confidential=data.get('is_confidential', False)
        )

        db.session.add(document_type)
        db.session.commit()

        # Log activity
        log_activity(
            user_id=current_user.id,
            action='document_type_add',
            description=f'إضافة نوع وثيقة جديد: {document_type.name_ar}',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )

        return {
            'success': True,
            'document_type': {
                'id': document_type.id,
                'name_ar': document_type.name_ar,
                'name_en': document_type.name_en,
                'priority': document_type.priority
            },
            'message': f'تم إنشاء نوع الوثيقة "{document_type.name_ar}" بنجاح'
        }

    except Exception as e:
        db.session.rollback()
        return {'success': False, 'message': f'حدث خطأ: {str(e)}'}, 500

if __name__ == '__main__':
    from database import init_database
    init_database()
    app.run(debug=True, host='0.0.0.0', port=5000)
