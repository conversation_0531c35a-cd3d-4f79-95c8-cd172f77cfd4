{% extends "base.html" %}

{% block title %}لوحة الإدارة - نظام الأرشيف الإلكتروني{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-cog me-2"></i>
            لوحة الإدارة
        </h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_users }}</h4>
                        <p class="card-text">إجمالي المستخدمين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_documents }}</h4>
                        <p class="card-text">إجمالي الوثائق</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_categories }}</h4>
                        <p class="card-text">التصنيفات النشطة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-folder fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات الإدارة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('manage_users') }}" class="btn btn-primary w-100">
                            <i class="fas fa-users me-2"></i>
                            إدارة المستخدمين
                        </a>
                    </div>

                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('add_user') }}" class="btn btn-success w-100">
                            <i class="fas fa-user-plus me-2"></i>
                            إضافة مستخدم جديد
                        </a>
                    </div>

                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('manage_categories') }}" class="btn btn-warning w-100">
                            <i class="fas fa-folder me-2"></i>
                            إدارة التصنيفات
                        </a>
                    </div>

                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('manage_document_types') }}" class="btn btn-info w-100">
                            <i class="fas fa-file-alt me-2"></i>
                            إدارة أنواع الوثائق
                        </a>
                    </div>

                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('search_documents') }}" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>
                            البحث في الوثائق
                        </a>
                    </div>

                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('dashboard') }}" class="btn btn-secondary w-100">
                            <i class="fas fa-home me-2"></i>
                            العودة للرئيسية
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    النشاطات الأخيرة
                </h5>
                <small class="text-muted">آخر 20 نشاط</small>
            </div>
            <div class="card-body">
                {% if recent_activities %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>المستخدم</th>
                                <th>النشاط</th>
                                <th>الوصف</th>
                                <th>التاريخ</th>
                                <th>عنوان IP</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for activity in recent_activities %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="user-avatar me-2">
                                            <i class="fas fa-user-circle fa-lg text-muted"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ activity.user.full_name }}</div>
                                            <small class="text-muted">{{ activity.user.username }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if activity.action == 'login' %}
                                        <span class="badge bg-success">تسجيل دخول</span>
                                    {% elif activity.action == 'logout' %}
                                        <span class="badge bg-secondary">تسجيل خروج</span>
                                    {% elif activity.action == 'upload' %}
                                        <span class="badge bg-primary">رفع وثيقة</span>
                                    {% elif activity.action == 'view' %}
                                        <span class="badge bg-info">عرض وثيقة</span>
                                    {% elif activity.action == 'download' %}
                                        <span class="badge bg-warning">تحميل وثيقة</span>
                                    {% elif activity.action == 'password_change' %}
                                        <span class="badge bg-danger">تغيير كلمة المرور</span>
                                    {% elif activity.action == 'user_add' %}
                                        <span class="badge bg-success">إضافة مستخدم</span>
                                    {% elif activity.action == 'user_edit' %}
                                        <span class="badge bg-warning">تعديل مستخدم</span>
                                    {% elif activity.action == 'user_deactivate' %}
                                        <span class="badge bg-danger">إلغاء تفعيل مستخدم</span>
                                    {% else %}
                                        <span class="badge bg-dark">{{ activity.action }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="activity-description">
                                        {{ activity.description }}
                                        {% if activity.document %}
                                        <br><small class="text-muted">الوثيقة: {{ activity.document.title[:50] }}{% if activity.document.title|length > 50 %}...{% endif %}</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <div class="activity-time">
                                        {{ activity.created_at.strftime('%Y-%m-%d') }}
                                        <br><small class="text-muted">{{ activity.created_at.strftime('%H:%M') }}</small>
                                    </div>
                                </td>
                                <td>
                                    <small class="text-muted">{{ activity.ip_address or 'غير محدد' }}</small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-history fa-3x mb-3"></i>
                    <p>لا توجد نشاطات حديثة</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        transition: box-shadow 0.15s ease-in-out;
    }

    .card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .user-avatar {
        width: 40px;
        text-align: center;
    }

    .activity-description {
        max-width: 300px;
        word-wrap: break-word;
    }

    .activity-time {
        white-space: nowrap;
    }

    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
        background-color: #f8f9fa;
    }

    .table tbody tr {
        transition: background-color 0.3s ease;
    }

    .table tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.05);
    }

    .btn {
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-1px);
    }
</style>
{% endblock %}
