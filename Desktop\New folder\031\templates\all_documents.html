{% extends "base.html" %}

{% block title %}جميع الوثائق - نظام الأرشيف الإلكتروني{% endblock %}

{% block extra_css %}
<style>
    .filter-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .document-card {
        transition: all 0.3s ease;
        border: 1px solid #e9ecef;
    }

    .document-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        border-color: #007bff;
    }

    .stats-card {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        border: none;
    }

    .table-responsive {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .btn-print {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        color: white;
    }

    .btn-print:hover {
        background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
        color: white;
    }

    .sort-link {
        color: #495057;
        text-decoration: none;
    }

    .sort-link:hover {
        color: #007bff;
    }

    .sort-active {
        color: #007bff;
        font-weight: bold;
    }

    @media print {
        .no-print {
            display: none !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0">
                <i class="fas fa-folder-open me-2 text-primary"></i>
                جميع الوثائق
            </h1>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-print" id="print-btn">
                    <i class="fas fa-print me-1"></i>طباعة القائمة
                </button>
                <a href="{{ url_for('upload_document') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>إضافة وثيقة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h3 class="mb-1">{{ total_documents }}</h3>
                <p class="mb-0">إجمالي الوثائق</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h3 class="mb-1">{{ filtered_count }}</h3>
                <p class="mb-0">النتائج المعروضة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h3 class="mb-1">{{ documents.pages }}</h3>
                <p class="mb-0">عدد الصفحات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h3 class="mb-1">{{ documents.page }}</h3>
                <p class="mb-0">الصفحة الحالية</p>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card filter-card mb-4 no-print">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-filter me-2"></i>فلترة وترتيب النتائج
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" id="filter-form">
            <div class="row">
                <!-- Search -->
                <div class="col-md-3 mb-3">
                    <label class="form-label">البحث</label>
                    <input type="text" class="form-control" name="search" value="{{ current_search or '' }}" placeholder="البحث في الوثائق...">
                </div>

                <!-- Category -->
                <div class="col-md-3 mb-3">
                    <label class="form-label">التصنيف</label>
                    <select class="form-select" name="category_id">
                        <option value="">جميع التصنيفات</option>
                        {% for category in categories %}
                            <option value="{{ category.id }}" {% if current_category == category.id|string %}selected{% endif %}>
                                {{ category.name_ar }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Document Type -->
                <div class="col-md-3 mb-3">
                    <label class="form-label">نوع الوثيقة</label>
                    <select class="form-select" name="document_type_id">
                        <option value="">جميع الأنواع</option>
                        {% for doc_type in document_types %}
                            <option value="{{ doc_type.id }}" {% if current_document_type == doc_type.id|string %}selected{% endif %}>
                                {{ doc_type.name_ar }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Registry Type -->
                <div class="col-md-3 mb-3">
                    <label class="form-label">نوع السجل</label>
                    <select class="form-select" name="registry_type">
                        <option value="">جميع السجلات</option>
                        <option value="incoming" {% if current_registry_type == 'incoming' %}selected{% endif %}>السجل الوارد</option>
                        <option value="outgoing" {% if current_registry_type == 'outgoing' %}selected{% endif %}>السجل الصادر</option>
                        <option value="general" {% if current_registry_type == 'general' %}selected{% endif %}>الأرشيف العام</option>
                    </select>
                </div>

                <!-- Date From -->
                <div class="col-md-3 mb-3">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" name="date_from" value="{{ current_date_from or '' }}">
                </div>

                <!-- Date To -->
                <div class="col-md-3 mb-3">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" name="date_to" value="{{ current_date_to or '' }}">
                </div>

                <!-- Confidential -->
                <div class="col-md-3 mb-3">
                    <label class="form-label">السرية</label>
                    <select class="form-select" name="is_confidential">
                        <option value="">جميع الوثائق</option>
                        <option value="true" {% if current_confidential == 'true' %}selected{% endif %}>سرية فقط</option>
                        <option value="false" {% if current_confidential == 'false' %}selected{% endif %}>غير سرية فقط</option>
                    </select>
                </div>

                <!-- Per Page -->
                <div class="col-md-3 mb-3">
                    <label class="form-label">عدد النتائج</label>
                    <select class="form-select" name="per_page">
                        <option value="10" {% if current_per_page == 10 %}selected{% endif %}>10</option>
                        <option value="20" {% if current_per_page == 20 %}selected{% endif %}>20</option>
                        <option value="50" {% if current_per_page == 50 %}selected{% endif %}>50</option>
                        <option value="100" {% if current_per_page == 100 %}selected{% endif %}>100</option>
                    </select>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>تطبيق الفلاتر
                    </button>
                    <a href="{{ url_for('all_documents') }}" class="btn btn-secondary">
                        <i class="fas fa-times me-1"></i>مسح الفلاتر
                    </a>
                </div>
            </div>

            <!-- Hidden fields for sorting -->
            <input type="hidden" name="sort_by" value="{{ current_sort_by }}">
            <input type="hidden" name="sort_order" value="{{ current_sort_order }}">
        </form>
    </div>
</div>

<!-- Documents Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>قائمة الوثائق
            {% if filtered_count != total_documents %}
                <span class="badge bg-info ms-2">{{ filtered_count }} من {{ total_documents }}</span>
            {% endif %}
        </h5>
    </div>
    <div class="card-body p-0">
        {% if documents.items %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>
                                <a href="?{{ request.query_string.decode() | replace('sort_by=' + current_sort_by, '') | replace('sort_order=' + current_sort_order, '') }}&sort_by=registry_number&sort_order={{ 'asc' if current_sort_by == 'registry_number' and current_sort_order == 'desc' else 'desc' }}"
                                   class="sort-link {{ 'sort-active' if current_sort_by == 'registry_number' else '' }}">
                                    رقم السجل
                                    {% if current_sort_by == 'registry_number' %}
                                        <i class="fas fa-sort-{{ 'up' if current_sort_order == 'asc' else 'down' }} ms-1"></i>
                                    {% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="?{{ request.query_string.decode() | replace('sort_by=' + current_sort_by, '') | replace('sort_order=' + current_sort_order, '') }}&sort_by=title&sort_order={{ 'asc' if current_sort_by == 'title' and current_sort_order == 'desc' else 'desc' }}"
                                   class="sort-link {{ 'sort-active' if current_sort_by == 'title' else '' }}">
                                    العنوان
                                    {% if current_sort_by == 'title' %}
                                        <i class="fas fa-sort-{{ 'up' if current_sort_order == 'asc' else 'down' }} ms-1"></i>
                                    {% endif %}
                                </a>
                            </th>
                            <th>التصنيف</th>
                            <th>نوع الوثيقة</th>
                            <th>
                                <a href="?{{ request.query_string.decode() | replace('sort_by=' + current_sort_by, '') | replace('sort_order=' + current_sort_order, '') }}&sort_by=document_date&sort_order={{ 'asc' if current_sort_by == 'document_date' and current_sort_order == 'desc' else 'desc' }}"
                                   class="sort-link {{ 'sort-active' if current_sort_by == 'document_date' else '' }}">
                                    تاريخ الوثيقة
                                    {% if current_sort_by == 'document_date' %}
                                        <i class="fas fa-sort-{{ 'up' if current_sort_order == 'asc' else 'down' }} ms-1"></i>
                                    {% endif %}
                                </a>
                            </th>
                            <th>الملف</th>
                            <th class="no-print">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for document in documents.items %}
                            <tr>
                                <td>
                                    <span class="badge bg-primary">{{ document.registry_number }}</span>
                                </td>
                                <td>
                                    <strong>{{ document.title }}</strong>
                                    {% if document.is_confidential %}
                                        <span class="badge bg-warning text-dark ms-1">
                                            <i class="fas fa-lock"></i> سري
                                        </span>
                                    {% endif %}
                                </td>
                                <td>{{ document.category.name_ar if document.category else '-' }}</td>
                                <td>{{ document.document_type.name_ar if document.document_type else '-' }}</td>
                                <td>{{ document.document_date.strftime('%Y-%m-%d') if document.document_date else '-' }}</td>
                                <td>
                                    <i class="{{ get_file_icon_class(document.filename) }} me-1"></i>
                                    {{ document.original_filename }}
                                    <small class="text-muted d-block">{{ format_file_size(document.file_size) }}</small>
                                </td>
                                <td class="no-print">
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('view_document', id=document.id) }}" class="btn btn-outline-primary" title="تفاصيل الوثيقة">
                                            <i class="fas fa-info-circle"></i>
                                        </a>
                                        <a href="{{ url_for('file_viewer', id=document.id) }}" class="btn btn-outline-info" title="عرض الملف">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('download_document', id=document.id) }}" class="btn btn-outline-success" title="تحميل">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        {% set file_ext = document.filename.split('.')[-1].lower() %}
                                        {% if file_ext in ['pdf', 'txt', 'jpg', 'jpeg', 'png', 'gif', 'bmp'] %}
                                            <a href="{{ url_for('print_file', id=document.id) }}" class="btn btn-outline-warning" title="طباعة الملف" target="_blank">
                                                <i class="fas fa-print"></i>
                                            </a>
                                        {% endif %}
                                        {% if current_user.role == 'admin' or document.uploaded_by == current_user.id %}
                                            <a href="{{ url_for('edit_document', id=document.id) }}" class="btn btn-outline-secondary" title="تعديل الوثيقة">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if documents.pages > 1 %}
                <div class="card-footer no-print">
                    <nav aria-label="تنقل الصفحات">
                        <ul class="pagination justify-content-center mb-0">
                            {% if documents.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="?{{ request.query_string.decode() | replace('page=' + documents.page|string, '') }}&page={{ documents.prev_num }}">السابق</a>
                                </li>
                            {% endif %}

                            {% for page_num in documents.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != documents.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="?{{ request.query_string.decode() | replace('page=' + documents.page|string, '') }}&page={{ page_num }}">{{ page_num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% if documents.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?{{ request.query_string.decode() | replace('page=' + documents.page|string, '') }}&page={{ documents.next_num }}">التالي</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد وثائق</h5>
                <p class="text-muted">لم يتم العثور على أي وثائق تطابق معايير البحث</p>
                <a href="{{ url_for('upload_document') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>إضافة وثيقة جديدة
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Print functionality
    $('#print-btn').on('click', function() {
        // Get current URL parameters
        const urlParams = new URLSearchParams(window.location.search);

        // Build print URL with same parameters
        let printUrl = '{{ url_for("print_documents") }}';
        if (urlParams.toString()) {
            printUrl += '?' + urlParams.toString();
        }

        // Open print page in new window
        window.open(printUrl, '_blank');
    });

    // Auto-submit form on filter change
    $('#filter-form select, #filter-form input[type="date"]').on('change', function() {
        $('#filter-form').submit();
    });

    // Search with delay
    let searchTimeout;
    $('#filter-form input[name="search"]').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            $('#filter-form').submit();
        }, 500);
    });
});
</script>
{% endblock %}
