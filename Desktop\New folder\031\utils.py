import os
import uuid
from datetime import datetime
from werkzeug.utils import secure_filename
from PIL import Image
from docx import Document as DocxDocument
import arabic_reshaper
from bidi.algorithm import get_display
from flask import current_app

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

def generate_unique_filename(original_filename):
    """Generate a unique filename while preserving the extension"""
    filename = secure_filename(original_filename)
    name, ext = os.path.splitext(filename)
    unique_name = f"{name}_{uuid.uuid4().hex[:8]}{ext}"
    return unique_name

def get_file_info(file_path):
    """Get file information including size and MIME type"""
    try:
        file_size = os.path.getsize(file_path)
        # Simple MIME type detection based on file extension
        ext = os.path.splitext(file_path)[1].lower()
        mime_types = {
            '.pdf': 'application/pdf',
            '.doc': 'application/msword',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.xls': 'application/vnd.ms-excel',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.txt': 'text/plain',
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.gif': 'image/gif'
        }
        mime_type = mime_types.get(ext, 'application/octet-stream')
        return {
            'size': file_size,
            'mime_type': mime_type
        }
    except Exception as e:
        return {
            'size': 0,
            'mime_type': 'application/octet-stream'
        }

def create_thumbnail(file_path, thumbnail_path, size=(200, 200)):
    """Create thumbnail for image files"""
    try:
        with Image.open(file_path) as img:
            img.thumbnail(size, Image.Resampling.LANCZOS)
            img.save(thumbnail_path, 'JPEG', quality=85)
            return True
    except Exception as e:
        print(f"Error creating thumbnail: {e}")
        return False

def extract_text_from_pdf(file_path, max_pages=5):
    """Extract text from PDF file for indexing - simplified version"""
    try:
        # For now, return empty string as PDF text extraction requires additional libraries
        # In production, you could use pdfplumber or PyPDF2
        return ""
    except Exception as e:
        print(f"Error extracting PDF text: {e}")
        return ""

def extract_text_from_docx(file_path):
    """Extract text from Word document"""
    try:
        doc = DocxDocument(file_path)
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        return text[:1000]  # Limit to first 1000 characters
    except Exception as e:
        print(f"Error extracting DOCX text: {e}")
        return ""

def format_arabic_text(text):
    """Format Arabic text for proper display"""
    if not text:
        return ""
    try:
        reshaped_text = arabic_reshaper.reshape(text)
        bidi_text = get_display(reshaped_text)
        return bidi_text
    except Exception as e:
        return text

def format_file_size(size_bytes):
    """Convert bytes to human readable format"""
    if size_bytes == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1

    return f"{size_bytes:.1f} {size_names[i]}"

def generate_registration_number(category_code="DOC", year=None):
    """Generate automatic registration number"""
    if year is None:
        year = datetime.now().year

    # This is a simple implementation - in production, you'd want to check the database
    # for the last number and increment it
    timestamp = datetime.now().strftime("%m%d%H%M")
    return f"{category_code}-{year}-{timestamp}"

def organize_file_path(filename, category_name, year=None):
    """Organize file storage path by category and year"""
    if year is None:
        year = datetime.now().year

    # Create directory structure: uploads/year/category/
    safe_category = secure_filename(category_name.replace(' ', '_'))
    relative_path = os.path.join(str(year), safe_category)
    full_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'documents', relative_path)

    # Create directory if it doesn't exist
    os.makedirs(full_path, exist_ok=True)

    return os.path.join(relative_path, filename)

def is_image_file(filename):
    """Check if file is an image"""
    image_extensions = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff'}
    return filename.lower().split('.')[-1] in image_extensions

def is_pdf_file(filename):
    """Check if file is a PDF"""
    return filename.lower().endswith('.pdf')

def is_office_file(filename):
    """Check if file is an Office document"""
    office_extensions = {'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'}
    return filename.lower().split('.')[-1] in office_extensions

def sanitize_search_query(query):
    """Sanitize search query to prevent SQL injection"""
    if not query:
        return ""

    # Remove special characters that could be used for SQL injection
    dangerous_chars = ['\'', '"', ';', '--', '/*', '*/', 'xp_', 'sp_']
    sanitized = query

    for char in dangerous_chars:
        sanitized = sanitized.replace(char, '')

    return sanitized.strip()

def log_activity(user_id, action, document_id=None, description=None, ip_address=None, user_agent=None):
    """Log user activity"""
    from models import ActivityLog, db

    activity = ActivityLog(
        user_id=user_id,
        document_id=document_id,
        action=action,
        description=description,
        ip_address=ip_address,
        user_agent=user_agent
    )

    try:
        db.session.add(activity)
        db.session.commit()
    except Exception as e:
        print(f"Error logging activity: {e}")
        db.session.rollback()

def get_file_icon_class(filename):
    """Get CSS class for file type icon"""
    extension = filename.lower().split('.')[-1] if '.' in filename else ''

    icon_map = {
        'pdf': 'fas fa-file-pdf text-danger',
        'doc': 'fas fa-file-word text-primary',
        'docx': 'fas fa-file-word text-primary',
        'xls': 'fas fa-file-excel text-success',
        'xlsx': 'fas fa-file-excel text-success',
        'ppt': 'fas fa-file-powerpoint text-warning',
        'pptx': 'fas fa-file-powerpoint text-warning',
        'txt': 'fas fa-file-alt text-secondary',
        'png': 'fas fa-file-image text-info',
        'jpg': 'fas fa-file-image text-info',
        'jpeg': 'fas fa-file-image text-info',
        'gif': 'fas fa-file-image text-info',
    }

    return icon_map.get(extension, 'fas fa-file text-secondary')
